# Deployment Guide

## 🚀 Pushing to GitHub

### 1. Create a GitHub Repository

1. Go to [GitHub](https://github.com) and sign in
2. Click the "+" icon in the top right corner
3. Select "New repository"
4. Name your repository (e.g., `pdf-form-filler` or `florida-contract-filler`)
5. Add a description: "PDF Form Filler for Florida Real Estate Contracts"
6. Choose Public or Private
7. **DO NOT** initialize with README, .gitignore, or license (we already have these)
8. Click "Create repository"

### 2. Connect Local Repository to GitHub

```bash
# Add the remote repository (replace with your GitHub URL)
git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### 3. Verify Upload

Your repository should now contain:
- ✅ Complete source code
- ✅ Documentation (README.md)
- ✅ Dependencies (requirements.txt)
- ✅ License file
- ✅ Proper .gitignore

## 🌐 Deployment Options

### Option 1: Local Development

```bash
# Clone and setup
git clone https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git
cd YOUR_REPOSITORY_NAME
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Run the application
python app.py &
streamlit run streamlit_pdf_form.py --server.port 8501
```

### Option 2: Heroku Deployment

1. **Create Procfile**
   ```
   web: python app.py
   streamlit: streamlit run streamlit_pdf_form.py --server.port $PORT
   ```

2. **Deploy to Heroku**
   ```bash
   heroku create your-app-name
   git push heroku main
   ```

### Option 3: Docker Deployment

1. **Create Dockerfile**
   ```dockerfile
   FROM python:3.9-slim
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   COPY . .
   EXPOSE 5001 8501
   CMD ["python", "app.py"]
   ```

2. **Build and run**
   ```bash
   docker build -t pdf-form-filler .
   docker run -p 5001:5001 -p 8501:8501 pdf-form-filler
   ```

## 🔧 Environment Configuration

### Production Settings

Create a `.env` file for production:

```env
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_PORT=5001
STREAMLIT_PORT=8501
SECRET_KEY=your-secret-key-here
```

### Security Considerations

1. **Never commit sensitive data**
   - API keys
   - Database credentials
   - Secret keys

2. **Use environment variables**
   - Configure through `.env` files
   - Use cloud provider environment settings

3. **Update dependencies regularly**
   ```bash
   pip list --outdated
   pip install --upgrade package-name
   ```

## 📊 Monitoring and Maintenance

### Health Checks

The application includes a health check endpoint:
```bash
curl http://localhost:5001/health
```

### Logs

Monitor application logs for errors:
```bash
# Local development
tail -f app.log

# Heroku
heroku logs --tail

# Docker
docker logs container-name
```

### Performance

- Monitor PDF generation times
- Check memory usage during large file processing
- Implement caching for frequently used templates

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt --upgrade

# Restart services
# (method depends on deployment platform)
```

### Backup Strategy

1. **Code**: Stored in Git repository
2. **PDF Templates**: Include in repository
3. **Generated Files**: Implement cleanup strategy
4. **Configuration**: Document environment variables

## 🆘 Troubleshooting

### Common Issues

1. **Port conflicts**
   - Change ports in configuration
   - Check for running processes

2. **PDF generation errors**
   - Verify PDF template exists
   - Check field mappings in pdf_fields.json

3. **Dependency issues**
   - Use virtual environment
   - Check Python version compatibility

4. **Memory issues**
   - Implement file cleanup
   - Monitor large PDF processing

### Support

- Check GitHub Issues
- Review application logs
- Test with comprehensive test endpoints
- Verify field mappings with diagnostic tools
