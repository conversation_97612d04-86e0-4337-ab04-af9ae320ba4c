#!/usr/bin/env python3
"""
Comprehensive test for all property description fields based on PDF screenshot analysis.
"""

import requests
import json

def test_property_fields():
    """Test all property description fields with realistic data."""
    
    # Based on PDF screenshot analysis
    test_data = {
        # Parties
        "Text_1": "<PERSON>",
        "Text_2": "<PERSON>",
        
        # Property Description Section 1
        "Text_3": "123 Ocean Drive, Miami Beach, FL 33139",  # (a) Street address
        "Text_4": "Miami-Dade",                              # (b) County
        "Text_5": "12-3456-789-0123",                       # (b) Tax ID
        "Text_6": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION, according to the plat thereof recorded in Plat Book 45, Page 67, of the Public Records of Miami-Dade County, Florida",  # (c) Legal description
        
        # Additional property details
        "Text_7": "Property includes pool, deck, landscaping, and all built-in appliances. Central air conditioning and heating system included.",  # Additional details
        
        # Personal Property (section 1d)
        "Text_9": "All kitchen appliances (refrigerator, dishwasher, microwave), window treatments, ceiling fans, and security system equipment",  # Personal property included
        
        # Excluded items (section 1e) 
        "Text_10": "Outdoor furniture, artwork, and personal decorative items are excluded from this sale",  # Excluded items
        
        # Financial
        "Number_1": "750000",    # Purchase price
        "Number_2": "75000",     # Initial deposit
        "Date_1": "2025-07-15",  # Contract date
        
        # Financing
        "Checkbox_9": "true",    # Conventional financing
        
        # Included appliances
        "Checkbox_16": "true",   # Refrigerator
        "Checkbox_17": "true",   # Washer
        "Checkbox_18": "true",   # Dryer
        "Checkbox_19": "true",   # Microwave
        "Checkbox_20": "true",   # Dishwasher
        "Checkbox_21": "true"    # Security system
    }
    
    # Send request to fill PDF
    response = requests.post(
        'http://localhost:5004/simple-fill',
        headers={'Content-Type': 'application/json'},
        json=test_data
    )
    
    if response.status_code == 200:
        # Save the filled PDF
        with open('comprehensive_property_test.pdf', 'wb') as f:
            f.write(response.content)
        print("✅ Comprehensive property test PDF created successfully!")
        print("📁 File saved as: comprehensive_property_test.pdf")
        
        # Print field mapping summary
        print("\n📋 FIELD MAPPING SUMMARY:")
        print("=" * 50)
        print("PROPERTY DESCRIPTION SECTION:")
        print(f"  (a) Street Address: Text_3 = '{test_data['Text_3']}'")
        print(f"  (b) County: Text_4 = '{test_data['Text_4']}'")
        print(f"  (b) Tax ID: Text_5 = '{test_data['Text_5']}'")
        print(f"  (c) Legal Desc: Text_6 = '{test_data['Text_6'][:50]}...'")
        print(f"  Additional Details: Text_7 = '{test_data['Text_7'][:50]}...'")
        print(f"  Personal Property: Text_9 = '{test_data['Text_9'][:50]}...'")
        print(f"  Excluded Items: Text_10 = '{test_data['Text_10'][:50]}...'")
        
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_property_fields()
