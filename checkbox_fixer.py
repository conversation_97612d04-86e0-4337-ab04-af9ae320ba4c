#!/usr/bin/env python3
"""
Alternative checkbox fixer using different PyPDF2 approach
"""

import PyPDF2
import io
from flask import Flask, request, send_file, jsonify

app = Flask(__name__)

def fix_checkboxes_alternative(pdf_path, field_data):
    """Alternative approach to fix checkboxes using PyPDF2"""
    
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        writer = PyPDF2.PdfWriter()
        
        # Copy all pages
        for page in reader.pages:
            writer.add_page(page)
        
        # Update form fields
        if reader.metadata is not None:
            writer.add_metadata(reader.metadata)
        
        # Try to update form fields using writer's update_page_form_field_values
        for page_num, page in enumerate(reader.pages):
            if '/Annots' in page:
                annotations = page['/Annots']
                
                for annotation_ref in annotations:
                    annotation = annotation_ref.get_object()
                    
                    if annotation.get('/Subtype') == '/Widget' and annotation.get('/T'):
                        field_name = str(annotation['/T']).strip("'\"()")
                        
                        if field_name in field_data:
                            field_value = field_data[field_name]
                            
                            # Handle checkboxes specifically
                            if annotation.get('/FT') == '/Btn':
                                if str(field_value).lower() in ['true', '1', 'yes', 'on']:
                                    # Try different checkbox values
                                    for check_val in ['/Yes', '/On', f'/{field_name}', '/1']:
                                        try:
                                            writer.update_page_form_field_values(
                                                writer.pages[page_num], 
                                                {field_name: check_val}
                                            )
                                            print(f"✅ Updated checkbox {field_name} = {check_val}")
                                            break
                                        except Exception as e:
                                            print(f"❌ Failed {field_name} with {check_val}: {e}")
                                            continue
                                else:
                                    writer.update_page_form_field_values(
                                        writer.pages[page_num], 
                                        {field_name: '/Off'}
                                    )
                            else:
                                # Handle text fields
                                writer.update_page_form_field_values(
                                    writer.pages[page_num], 
                                    {field_name: str(field_value)}
                                )
        
        # Save to memory
        output_stream = io.BytesIO()
        writer.write(output_stream)
        output_stream.seek(0)
        
        return output_stream.getvalue()

@app.route('/fix-checkboxes', methods=['POST'])
def fix_checkboxes():
    """Test the alternative checkbox fixing approach"""
    try:
        data = request.get_json()
        
        # Fill the PDF
        filled_pdf_bytes = fix_checkboxes_alternative('as is contract fillable.pdf', data)
        
        # Return the filled PDF
        return send_file(
            io.BytesIO(filled_pdf_bytes),
            mimetype='application/pdf',
            as_attachment=True,
            download_name='checkbox_fixed.pdf'
        )
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/')
def home():
    return jsonify({
        "message": "Checkbox Fixer API",
        "endpoints": {
            "/fix-checkboxes": "POST - Fix checkboxes with alternative method"
        }
    })

if __name__ == '__main__':
    app.run(host='127.0.0.1', port=5005, debug=True)
