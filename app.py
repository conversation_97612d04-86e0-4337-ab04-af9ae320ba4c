from flask import Flask, request, send_file, jsonify
from pdfrw import Pdf<PERSON><PERSON><PERSON>, PdfWriter, PageMerge, PdfDict
import os
import io
import json
import re

app = Flask(__name__)

# Path to your fillable PDF form template
PDF_TEMPLATE_PATH = 'as is contract fillable.pdf'

# Load PDF fields for debugging
def load_pdf_fields():
    try:
        with open('pdf_fields.json', 'r') as f:
            return json.load(f)
    except:
        return {}

PDF_FIELDS = load_pdf_fields()



# FINAL FIELD MAPPING (CLEANED + FUNCTIONAL)

FIELD_MAP = {
    # === PARTIES SECTION ===
    "seller_name": "Text_1",  # Confirmed this is seller
    "buyer_name": "Text_2",  
    "seller_address": "Text_3",
    "buyer_address": "Text_4",
    
    # === PROPERTY DESCRIPTION ===
    "street_address": "Text_5",  # (a) Street address, city, zip
    "county": "Text_6",         # (b) Located in [County]
    "tax_id": "Text_7",         # Real Property Tax ID No
    "legal_description": "Text_8",  # (c) Legal description
    
    # === PURCHASE TERMS ===
    "purchase_price": "Number_1",
    "initial_deposit_amount": "Number_2",
    "deposit_holder": "Text_9",  # Escrow Agent name
    "additional_deposit": "Number_3",
    "loan_amount": "Number_4",
    "balance_at_closing": "Number_5",
    
    # === DATES ===
    "contract_date": "Date_1",
    "effective_date": "Date_2", 
    "closing_date": "Text_10",  # Closing Date field
    "inspection_period": "Number_6",  # Days for inspection
    
    # === FINANCING ===
    "financing_type": [  # Checkbox group for financing
        "Checkbox_1",    # Cash
        "Checkbox_2",    # Conventional
        "Checkbox_3",    # FHA
        "Checkbox_4"     # VA
    ],
    "interest_rate": "Number_7",
    "loan_term": "Number_8",
    
    # === PERSONAL PROPERTY INCLUDED ===
    "included_items": [  # Checkboxes for appliances
        "Checkbox_5",    # Refrigerator
        "Checkbox_6",    # Washer
        "Checkbox_7",    # Dryer
        # ... (all other appliance checkboxes)
    ],
    "other_included_items": "Text_11",  # Freeform text
    
    # === ESCROW AGENT ===
    "escrow_agent_name": "Text_12",
    "escrow_agent_address": "Text_13",
    "escrow_agent_phone": "US_Phone_Number_1",
    "escrow_agent_email": "Email_1",
    
    # === SIGNATURES ===
    "buyer_signature": "Signature_1",
    "seller_signature": "Signature_2", 
    "buyer_sign_date": "Date_3",
    "seller_sign_date": "Date_4",
    
    # === BROKER INFORMATION ===
    "listing_broker": "Text_14",
    "listing_agent": "Text_15",
    "cooperating_broker": "Text_16",
    "cooperating_agent": "Text_17",
    
    # === SPECIAL PROVISIONS ===
    "special_clauses": "Text_18",  # Additional terms
    "flood_zone_acknowledgement": "Checkbox_8",
    "lead_paint_disclosure": "Checkbox_9"
}

# Helper function to extract tax ID from combined field
def extract_tax_id(tax_field_value):
    """Extract just the tax ID portion from a combined field like 'Miami-Dade County, FL Tax ID: 12-3456-789-0123'"""
    if not tax_field_value:
        return ""
    
    # Look for pattern like "Tax ID: 12-3456-789-0123"
    match = re.search(r'Tax ID:\s*([0-9\-]+)', tax_field_value)
    if match:
        return match.group(1)
    
    # If no "Tax ID:" pattern, return the original value
    return tax_field_value

# Helper function to process form data and apply field mappings
def process_form_data(form_data):
    """SIMPLIFIED: Process incoming form data and map to PDF field names"""
    processed_data = {}

    # Simple direct mapping - no complex combination logic needed with clean numbered fields
    for user_field, value in form_data.items():
        if user_field in FIELD_MAP:
            pdf_field = FIELD_MAP[user_field]
            processed_data[pdf_field] = value
            print(f"DEBUG: Mapped {user_field} -> {pdf_field} = '{value}'")
        else:
            # If no mapping found, try direct field name (for backwards compatibility)
            processed_data[user_field] = value
            print(f"DEBUG: Direct field {user_field} = '{value}'")

    print(f"DEBUG: Processed {len(processed_data)} total fields")
    return processed_data

# Helper function to set PDF fields with proper type handling
def set_pdf_fields(template_path, field_data):
    """Fill PDF form fields with provided data"""
    try:
        template_pdf = PdfReader(template_path)

        # Process the form data through our mapping
        processed_data = process_form_data(field_data)

        print(f"DEBUG: Input data keys: {list(processed_data.keys())}")

        filled_fields = []

        for page in template_pdf.pages:
            if page.Annots:
                for annotation in page.Annots:
                    if annotation.Subtype == '/Widget' and annotation.T:
                        field_name = annotation.T[1:-1]  # Remove parentheses

                        # Try to find a value for this field
                        field_value = None

                        # Direct match with parentheses
                        if f"({field_name})" in processed_data:
                            field_value = processed_data[f"({field_name})"]
                            print(f"DEBUG: Direct match (with parens) ({field_name}) = '{field_value}'")
                        # Direct match without parentheses
                        elif field_name in processed_data:
                            field_value = processed_data[field_name]
                            print(f"DEBUG: Direct match (without parens) {field_name} = '{field_value}'")

                        if field_value is not None:
                            # Handle different field types
                            if annotation.FT == '/Tx':  # Text field
                                annotation.V = str(field_value)
                                annotation.AP = ''  # Clear appearance to force regeneration
                                print(f"DEBUG: Set text field ({field_name}) = '{field_value}'")
                            elif annotation.FT == '/Btn':  # Button/Checkbox field
                                # Handle checkbox values
                                if str(field_value).lower() in ['true', '1', 'yes', 'on', '/yes']:
                                    annotation.V = '/Yes'
                                    annotation.AS = '/Yes'
                                else:
                                    annotation.V = '/Off'
                                    annotation.AS = '/Off'
                                print(f"DEBUG: Set checkbox ({field_name}) = {annotation.V}")

                            filled_fields.append((f"({field_name})", str(field_value)))

        print(f"DEBUG: Filled {len(filled_fields)} fields")
        return template_pdf, filled_fields

    except Exception as e:
        print(f"ERROR in set_pdf_fields: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

# ===== MAIN API ENDPOINT =====
@app.route('/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Main endpoint for generating filled PDF forms
    Accepts JSON payload with friendly field names and returns filled PDF
    """
    try:
        # Get form data from request
        form_data = request.json
        if not form_data:
            return jsonify({"error": "No form data provided"}), 400

        print(f"DEBUG: Received form data: {form_data}")

        # Fill the PDF with the provided data
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, form_data)

        # Create output buffer
        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        print(f"DEBUG: PDF generated successfully, size: {len(output_buffer.getvalue())} bytes")

        # Return the filled PDF as a downloadable file
        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='filled_florida_contract.pdf'
        )

    except Exception as e:
        print(f"ERROR in generate_pdf: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

# ===== ADDITIONAL API ENDPOINTS =====

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "PDF Form Filler API is running"})

@app.route('/fields', methods=['GET'])
def get_available_fields():
    """Return list of available field mappings"""
    return jsonify({
        "available_fields": list(FIELD_MAP.keys()),
        "total_fields": len(FIELD_MAP),
        "description": "Use these field names in your JSON payload to /generate-pdf"
    })

@app.route('/test-sample', methods=['GET'])
def test_sample():
    """Generate a test PDF with sample data"""
    sample_data = {
        "buyerName": "John Smith (\"Buyer\")",
        "sellerName": "Sarah Johnson (\"Seller\")",
        "parties": "John Smith (\"Buyer\") and Sarah Johnson (\"Seller\")",
        "propertyDescription": "Beautiful 3-bedroom, 2-bathroom single-family home with pool and 2-car garage",
        "streetAddress": "123 Sunset Boulevard, Miami, FL 33101",
        "propertyTaxID": "Miami-Dade County, FL Tax ID: 12-3456-789-0123",
        "legalDescription1": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "legalDescription2": "according to the plat thereof recorded in Plat Book 45, Page 67, Public Records of Miami-Dade County, Florida",
        "purchasePrice": "$750,000.00",
        "initialDeposit": "$75,000.00",
        "buyerInitials": "JS",
        "sellerInitials": "SJ",
        "conventionalFinancing": "true",
        "fixedRate": "true"
    }

    try:
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, sample_data)

        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='sample_filled_contract.pdf'
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5002)
