from flask import Flask, request, send_file, jsonify
from pdfrw import Pdf<PERSON><PERSON><PERSON>, PdfWriter, PageMerge, PdfDict
import os
import io
import json
import re

app = Flask(__name__)

# Path to your fillable PDF form template
PDF_TEMPLATE_PATH = 'Florida-As_Is-Purchase-Agreement.pdf'

# Load PDF fields for debugging
def load_pdf_fields():
    try:
        with open('pdf_fields.json', 'r') as f:
            return json.load(f)
    except:
        return {}

PDF_FIELDS = load_pdf_fields()

# COMPREHENSIVE FIELD MAPPING
# Maps friendly user-defined names to actual PDF field names
FIELD_MAP = {
    # ===== CORE PROPERTY INFORMATION =====
    # Individual buyer/seller names (will be combined in processing)
    "buyerName": "BUYER_NAME_TEMP",  # Special handling - combined into PARTIES
    "sellerName": "SELLER_NAME_TEMP",  # Special handling - combined into PARTIES
    "parties": "(PARTIES)",  # Direct parties field (if pre-formatted)

    # Individual county/tax ID (will be combined in processing)
    "county": "COUNTY_TEMP",  # Special handling - combined into County Florida Property Tax ID
    "propertyTaxID": "TAX_ID_TEMP",  # Special handling - combined into County Florida Property Tax ID

    # Other property fields
    "propertyDescription": "(PROPERTY DESCRIPTION)",
    "propertyLocation": "(PROPERTY DESCRIPTION)",
    "streetAddress": "(a Street address city zip)",
    "propertyAddress": "(a Street address city zip)",
    "legalDescription1": "(c Real Property The legal description is 1)",
    "legalDescription2": "(c Real Property The legal description is 2)",
    "additionalAddress": "(Address)",
    
    # ===== FINANCIAL FIELDS =====
    "purchasePrice": "(Text79)",
    "initialDeposit": "(Text80)",
    "additionalDeposit": "(Text81)",
    "balanceToClose": "(Text82)",
    "earnestMoney": "(Text83)",
    "inspectionFee": "(Text84)",
    "titleInsurance": "(Text85)",
    "recordingFees": "(Text86)",
    "closingCosts": "(Text87)",
    "loanAmount": "(Text88)",
    "downPayment": "(Text89)",
    "interestRate": "(Text90)",
    "monthlyPayment": "(Text91)",
    "additionalFees": "(Text92)",
    "prorationAmount": "(Text93)",
    "escrowAmount": "(Text94)",
    "commissionAmount": "(Text95)",
    "otherAmount": "(Text96)",
    "finalAmount": "(Text103)",
    
    # ===== INITIALS FIELDS =====
    "buyerInitials": "Buyers Initials",
    "buyerInitials2": "Buyers Initials_2", 
    "sellerInitials": "Sellers Initials",
    "sellerInitials2": "Sellers Initials_2",
    "sellerInitials3": "Sellers Initials_3",
    "sellerInitials4": "Sellers Initials_4",
    
    # ===== CONTACT INFORMATION =====
    "escrowAgentName": "Escrow Agent Information Name",
    "escrowAddress": "Address",
    "escrowEmail": "Email", 
    "escrowFax": "Fax",
    "buyerAddress1": "Buyers address for purposes of notice 1",
    "buyerAddress2": "Buyers address for purposes of notice 2", 
    "buyerAddress3": "Buyers address for purposes of notice 3",
    "sellerAddress1": "Sellers address for purposes of notice 1",
    "sellerAddress2": "Sellers address for purposes of notice 2",
    "sellerAddress3": "Sellers address for purposes of notice 3",
    
    # ===== DATES =====
    "closingDate": "Closing Date at the time established by the Closing Agent",
    "contractDate": "Date",
    "effectiveDate": "Date_2",
    
    # ===== REAL ESTATE PROFESSIONALS =====
    "cooperatingBroker": "Cooperating Broker if any",
    "cooperatingSalesAssociate": "Cooperating Sales Associate if any", 
    "listingSalesAssociate": "Listing Sales Associate",
    "listingBroker": "Listing Broker",
    
    # ===== PERSONAL PROPERTY =====
    "includedPersonalProperty": "Personal Property is included in the Purchase Price has no contributory value and shall be left for the Buyer",
    "otherPersonalProperty": "Other Personal Property items included in this purchase are",
    "excludedItems": "e The following items are excluded from the purchase",
    "accessDevices": "and other access devices and storm shutterspanels Personal Property",
    
    # ===== CHECKBOX FIELDS (FINANCING OPTIONS) =====
    "cashPurchase": "(a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers)",
    "financingContingent": "(b This Contract is contingent upon Buyer obtaining approval of a)",
    "conventionalFinancing": "(conventional)",
    "fhaFinancing": "(FHA)",
    "vaFinancing": "(VA or)",
    "otherFinancing": "(other)",
    "fixedRate": "(fixed)",
    "adjustableRate": "(adjustable)",
    "assumptionMortgage": "(c Assumption of existing mortgage see rider for terms)",
    "sellerFinancing": "(d Purchase money note and mortgage to Seller see riders addenda or special clauses for terms)",
    
    # ===== TITLE AND CLOSING CHECKBOXES =====
    "sellerDesignatesClosing": "(i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the)",
    "buyerDesignatesClosing": "(ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing)",
    "miamiDadeBroward": "(iii MIAMIDADEBROWARD REGIONAL PROVISION Seller shall furnish a copy of a prior owners policy)",
    "buyerPaysWarranty": "(Buyer)",
    "sellerPaysWarranty": "(Seller)",
    "homeWarrantyNA": "(NA shall pay for a home warranty plan issued by)",
    
    # ===== ASSESSMENT CHECKBOXES =====
    "assessmentProration": "(a Seller shall pay installments due prior to Closing and Buyer shall pay installments due after Closing)",
    "assessmentPaidFull": "(b Seller shall pay the assessments in full prior to or at the time of Closing)",
    
    # ===== SPECIAL PROVISIONS AND RIDERS =====
    "condominiumRider": "(A Condominium Rider)",
    "homeownersAssociation": "(B Homeowners Assn)",
    "sellerFinancingRider": "(C Seller Financing)",
    "mortgageAssumption": "(D Mortgage Assumption)",
    "fhaVaFinancing": "(E FHAVA Financing)",
    "appraisalContingency": "(F Appraisal Contingency)",
    "shortSale": "(G Short Sale)",
    "homeownersFloodIns": "(H HomeownersFlood Ins)",
    "defectiveDrywall": "(M Defective Drywall)",
    "coastalConstruction": "(N Coastal Construction Control)",
    "insulationDisclosure": "(O Insulation Disclosure)",
    "leadPaintDisclosure": "(P Lead Paint Disclosure Pre1978)",
    "housingOlderPersons": "(Q Housing for Older Persons)",
    "rezoning": "(R Rezoning)",
    "leasePurchase": "(S Lease Purchase Lease Option)",
    "preClosingOccupancy": "(T PreClosing Occupancy)",
    "saleOfBuyersProperty": "(V Sale of Buyers Property)",
    "backupContract": "(W Backup Contract)",
    "sellersAttorneyApproval": "(Y Sellers Attorney Approval)",
    "buyersAttorneyApproval": "(Z Buyers Attorney Approval)",
    "bindingArbitration": "(BB Binding Arbitration)",
    "miamiDadeCounty": "(CC MiamiDade County)",
    
    # ===== GENERIC CHECKBOXES =====
    "checkbox1": "(Check Box97)",
    "checkbox2": "(Check Box99)",
    "checkbox3": "(Check Box101)",
    "checkbox4": "(Check Box102)",
    "genericI": "(I)",
    "genericJ": "(J)",
    "reservedK": "(K RESERVED)",
    "reservedL": "(L RESERVED)",
    
    # ===== OFFER RESPONSE CHECKBOXES =====
    "sellerCounters": "(Seller counters Buyers offer to accept the counteroffer Buyer must sign or initial the counteroffered terms and)",
    "sellerRejects": "(Seller rejects Buyers offer)",
    
    # ===== UNDEFINED FIELDS (SPECIAL MAPPINGS) =====
    "specialField1": "(undefined_2)",
    "specialField2": "(undefined_3)", 
    "specialField3": "(undefined_4)",
    "specialField4": "(undefined_5)",
    "specialField5": "(undefined_6)",
    "specialField6": "(undefined_7)",
    "specialField7": "(undefined_8)",
    "specialField8": "(undefined_9)",
    "specialField9": "(undefined_10)",
    "specialField10": "(undefined_11)",
    "specialField11": "(undefined_12)",
    "specialField12": "(undefined_13)",
    "specialField13": "(undefined_14)",
    "specialField14": "(undefined_15)",
    
    # ===== LEGACY COMPATIBILITY MAPPINGS =====
    "FIELD-PARTIES": "(PARTIES)",
    "FIELD-PROPERTY-DESC": "(PROPERTY DESCRIPTION)",
    "FIELD-STREET-ADDRESS": "(a Street address city zip)",
    "FIELD-COUNTY-TAX-ID": "(County Florida Property Tax ID)",
    "FIELD-LEGAL-DESC-1": "(c Real Property The legal description is 1)",
    "FIELD-LEGAL-DESC-2": "(c Real Property The legal description is 2)",
}

# Helper function to extract tax ID from combined field
def extract_tax_id(tax_field_value):
    """Extract just the tax ID portion from a combined field like 'Miami-Dade County, FL Tax ID: 12-3456-789-0123'"""
    if not tax_field_value:
        return ""
    
    # Look for pattern like "Tax ID: 12-3456-789-0123"
    match = re.search(r'Tax ID:\s*([0-9\-]+)', tax_field_value)
    if match:
        return match.group(1)
    
    # If no "Tax ID:" pattern, return the original value
    return tax_field_value

# Helper function to process form data and apply field mappings
def process_form_data(form_data):
    """Process incoming form data and map to PDF field names"""
    processed_data = {}

    # Temporary storage for combination fields
    buyer_name = None
    seller_name = None
    county = None
    tax_id = None

    # First pass: collect individual values and process regular fields
    for user_field, value in form_data.items():
        if user_field in FIELD_MAP:
            pdf_field = FIELD_MAP[user_field]

            # Handle special combination fields
            if pdf_field == "BUYER_NAME_TEMP":
                buyer_name = str(value) if value else ""
                continue
            elif pdf_field == "SELLER_NAME_TEMP":
                seller_name = str(value) if value else ""
                continue
            elif pdf_field == "COUNTY_TEMP":
                county = str(value) if value else ""
                continue
            elif pdf_field == "TAX_ID_TEMP":
                tax_id = str(value) if value else ""
                continue

            # Regular field processing
            processed_data[pdf_field] = value
        else:
            # If no mapping found, try direct field name (for backwards compatibility)
            processed_data[user_field] = value

    # Second pass: Create combination fields

    # Combine buyer and seller names into PARTIES field
    if buyer_name or seller_name:
        if buyer_name and seller_name:
            parties_text = f'{buyer_name} (\\"Buyer\\") and {seller_name} (\\"Seller\\")'
        elif buyer_name:
            parties_text = f'{buyer_name} (\\"Buyer\\")'
        elif seller_name:
            parties_text = f'{seller_name} (\\"Seller\\")'
        else:
            parties_text = ""

        processed_data["(PARTIES)"] = parties_text
        print(f"DEBUG: Combined PARTIES field: '{parties_text}'")

    # Combine county and tax ID into County Florida Property Tax ID field
    if county or tax_id:
        if county and tax_id:
            # Clean the tax ID if it contains extra text
            clean_tax_id = extract_tax_id(tax_id)
            # Remove "County" suffix if it exists to avoid duplication
            clean_county = county.replace(" County", "").replace(" county", "")
            county_tax_text = f"{clean_county} County, Florida. Property Tax ID #: {clean_tax_id}"
        elif county:
            clean_county = county.replace(" County", "").replace(" county", "")
            county_tax_text = f"{clean_county} County, Florida. Property Tax ID #: "
        elif tax_id:
            clean_tax_id = extract_tax_id(tax_id)
            county_tax_text = f"County, Florida. Property Tax ID #: {clean_tax_id}"
        else:
            county_tax_text = ""

        processed_data["(County Florida Property Tax ID)"] = county_tax_text
        print(f"DEBUG: Combined County/Tax ID field: '{county_tax_text}'")

    return processed_data

# Helper function to set PDF fields with proper type handling
def set_pdf_fields(template_path, field_data):
    """Fill PDF form fields with provided data"""
    try:
        template_pdf = PdfReader(template_path)

        # Process the form data through our mapping
        processed_data = process_form_data(field_data)

        print(f"DEBUG: Input data keys: {list(processed_data.keys())}")

        filled_fields = []

        for page in template_pdf.pages:
            if page.Annots:
                for annotation in page.Annots:
                    if annotation.Subtype == '/Widget' and annotation.T:
                        field_name = annotation.T[1:-1]  # Remove parentheses

                        # Try to find a value for this field
                        field_value = None

                        # Direct match with parentheses
                        if f"({field_name})" in processed_data:
                            field_value = processed_data[f"({field_name})"]
                            print(f"DEBUG: Direct match (with parens) ({field_name}) = '{field_value}'")
                        # Direct match without parentheses
                        elif field_name in processed_data:
                            field_value = processed_data[field_name]
                            print(f"DEBUG: Direct match (without parens) {field_name} = '{field_value}'")

                        if field_value is not None:
                            # Handle different field types
                            if annotation.FT == '/Tx':  # Text field
                                annotation.V = str(field_value)
                                annotation.AP = ''  # Clear appearance to force regeneration
                                print(f"DEBUG: Set text field ({field_name}) = '{field_value}'")
                            elif annotation.FT == '/Btn':  # Button/Checkbox field
                                # Handle checkbox values
                                if str(field_value).lower() in ['true', '1', 'yes', 'on', '/yes']:
                                    annotation.V = '/Yes'
                                    annotation.AS = '/Yes'
                                else:
                                    annotation.V = '/Off'
                                    annotation.AS = '/Off'
                                print(f"DEBUG: Set checkbox ({field_name}) = {annotation.V}")

                            filled_fields.append((f"({field_name})", str(field_value)))

        print(f"DEBUG: Filled {len(filled_fields)} fields")
        return template_pdf, filled_fields

    except Exception as e:
        print(f"ERROR in set_pdf_fields: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

# ===== MAIN API ENDPOINT =====
@app.route('/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Main endpoint for generating filled PDF forms
    Accepts JSON payload with friendly field names and returns filled PDF
    """
    try:
        # Get form data from request
        form_data = request.json
        if not form_data:
            return jsonify({"error": "No form data provided"}), 400

        print(f"DEBUG: Received form data: {form_data}")

        # Fill the PDF with the provided data
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, form_data)

        # Create output buffer
        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        print(f"DEBUG: PDF generated successfully, size: {len(output_buffer.getvalue())} bytes")

        # Return the filled PDF as a downloadable file
        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='filled_florida_contract.pdf'
        )

    except Exception as e:
        print(f"ERROR in generate_pdf: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

# ===== ADDITIONAL API ENDPOINTS =====

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "PDF Form Filler API is running"})

@app.route('/fields', methods=['GET'])
def get_available_fields():
    """Return list of available field mappings"""
    return jsonify({
        "available_fields": list(FIELD_MAP.keys()),
        "total_fields": len(FIELD_MAP),
        "description": "Use these field names in your JSON payload to /generate-pdf"
    })

@app.route('/test-sample', methods=['GET'])
def test_sample():
    """Generate a test PDF with sample data"""
    sample_data = {
        "buyerName": "John Smith (\"Buyer\")",
        "sellerName": "Sarah Johnson (\"Seller\")",
        "parties": "John Smith (\"Buyer\") and Sarah Johnson (\"Seller\")",
        "propertyDescription": "Beautiful 3-bedroom, 2-bathroom single-family home with pool and 2-car garage",
        "streetAddress": "123 Sunset Boulevard, Miami, FL 33101",
        "propertyTaxID": "Miami-Dade County, FL Tax ID: 12-3456-789-0123",
        "legalDescription1": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "legalDescription2": "according to the plat thereof recorded in Plat Book 45, Page 67, Public Records of Miami-Dade County, Florida",
        "purchasePrice": "$750,000.00",
        "initialDeposit": "$75,000.00",
        "buyerInitials": "JS",
        "sellerInitials": "SJ",
        "conventionalFinancing": "true",
        "fixedRate": "true"
    }

    try:
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, sample_data)

        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='sample_filled_contract.pdf'
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5002)
