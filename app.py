from flask import Flask, request, send_file, jsonify
from pdfrw import Pdf<PERSON><PERSON><PERSON>, PdfWriter, PageMerge, PdfDict
import os
import io
import json
import re

app = Flask(__name__)

# Path to your fillable PDF form template
PDF_TEMPLATE_PATH = 'as is contract fillable.pdf'

# Load PDF fields for debugging
def load_pdf_fields():
    try:
        with open('pdf_fields.json', 'r') as f:
            return json.load(f)
    except:
        return {}

PDF_FIELDS = load_pdf_fields()



# FINAL FIELD MAPPING (CLEANED + FUNCTIONAL)

FIELD_MAP = {
    # === PARTIES SECTION ===
    "seller_name": "Text_1",
    "buyer_name": "Text_2",
    "seller_address": "Text_3",
    "buyer_address": "Text_4",

    # === PROPERTY DESCRIPTION ===
    "street_address": "Text_5",
    "county": "Text_6",
    "tax_id": "Text_7",
    "legal_description": "Text_8",

    # === PURCHASE TERMS ===
    "purchase_price": "Number_1",
    "initial_deposit_amount": "Number_2",
    "deposit_holder": "Text_9",
    "additional_deposit": "Number_3",
    "loan_amount": "Number_4",
    "balance_at_closing": "Number_5",

    # === DATES & DEADLINES ===
    "contract_date": "Date_1",
    "effective_date": "Date_2",
    "closing_date": "Text_10",
    "inspection_period": "Number_6",
    "financing_deadline": "Number_7",

    # === FINANCING CHECKBOXES (8-15) ===
    "financing_type": {
        "cash": "Checkbox_8",
        "conventional": "Checkbox_9",
        "fha": "Checkbox_10",
        "va": "Checkbox_11",
        "other_financing": "Checkbox_12",
        "fixed_rate": "Checkbox_13",
        "adjustable_rate": "Checkbox_14",
        "assumption": "Checkbox_15"
    },

    # === PERSONAL PROPERTY CHECKBOXES (16-39) ===
    "included_items": {
        "refrigerator": "Checkbox_16",
        "washer": "Checkbox_17",
        "dryer": "Checkbox_18",
        "microwave": "Checkbox_19",
        "security_system": "Checkbox_20",
        "pool_equipment": "Checkbox_21",
        "tv_antenna": "Checkbox_22",
        "window_ac": "Checkbox_23",
        "pool_heater": "Checkbox_24",
        "water_softener": "Checkbox_25",
        "generator": "Checkbox_26",
        "hot_tub": "Checkbox_27",
        "storm_shutters": "Checkbox_28",
        "ice_maker": "Checkbox_29",
        "smoke_detectors": "Checkbox_30",
        "pool_fence": "Checkbox_31",
        "storage_shed": "Checkbox_32",
        "ceiling_fans": "Checkbox_33",
        "light_fixtures": "Checkbox_34",
        "window_treatments": "Checkbox_35",
        "garage_openers": "Checkbox_36",
        "security_gate": "Checkbox_37",
        "intercom": "Checkbox_38",
        "disposal": "Checkbox_39"
    },

    # === DEPOSIT OPTIONS (40-42) ===
    "deposit_timing": {
        "with_offer": "Checkbox_40",
        "upon_acceptance": "Checkbox_41",
        "after_effective_date": "Checkbox_42"
    },

    # === ESCROW AGENT ===
    "escrow_agent_name": "Text_11",
    "escrow_agent_address": "Text_12",
    "escrow_agent_phone": "US_Phone_Number_1",
    "escrow_agent_email": "Email_1",
    "escrow_agent_fax": "Text_13",

    # === TITLE OPTIONS (43-45) ===
    "title_insurance_option": {
        "seller_pays": "Checkbox_43",
        "buyer_pays": "Checkbox_44",
        "regional_provision": "Checkbox_45"
    },

    # === HOME WARRANTY (46-48) ===
    "home_warranty": {
        "buyer_pays": "Checkbox_46",
        "seller_pays": "Checkbox_47",
        "not_applicable": "Checkbox_48"
    },

    # === ASSESSMENTS (49-50) ===
    "special_assessments": {
        "prorated": "Checkbox_49",
        "paid_in_full": "Checkbox_50"
    },

    # === ASSIGNMENT OPTIONS (51-53) ===
    "assignability": {
        "assign_with_release": "Checkbox_51",
        "assign_without_release": "Checkbox_52",
        "no_assignment": "Checkbox_53"
    },

    # === DISCLOSURE ACKNOWLEDGMENTS (54-72) ===
    "disclosures": {
        "radon": "Checkbox_54",
        "permits": "Checkbox_55",
        "mold": "Checkbox_56",
        "flood_zone": "Checkbox_57",
        "energy_efficiency": "Checkbox_58",
        "lead_paint": "Checkbox_59",
        "hoa": "Checkbox_60",
        "property_taxes": "Checkbox_61",
        "firpta": "Checkbox_62",
        "seller_disclosure": "Checkbox_63",
        "as_is": "Checkbox_64",
        "inspection_contingency": "Checkbox_65",
        "appraisal_contingency": "Checkbox_66",
        "walkthrough": "Checkbox_67",
        "permit_cooperation": "Checkbox_68",
        "warranty_assignments": "Checkbox_69",
        "risk_of_loss": "Checkbox_70",
        "1031_exchange": "Checkbox_71",
        "attorney_approval": "Checkbox_72"
    },

    # === SIGNATURES ===
    "buyer_signature": "Signature_1",
    "seller_signature": "Signature_2",
    "buyer_sign_date": "Date_3",
    "seller_sign_date": "Date_4",

    # === BROKER INFORMATION ===
    "listing_broker": "Text_14",
    "listing_agent": "Text_15",
    "cooperating_broker": "Text_16",
    "cooperating_agent": "Text_17",

    # === NOTICE ADDRESSES ===
    "buyer_notice_address1": "Text_18",
    "buyer_notice_address2": "Text_19",
    "seller_notice_address1": "Text_20",
    "seller_notice_address2": "Text_21",

    # === ADDENDA CHECKBOXES (73-144) ===
    "addenda": {
        "condominium": "Checkbox_73",
        "hoa": "Checkbox_74",
        "seller_financing": "Checkbox_75",
        "mortgage_assumption": "Checkbox_76",
        "fha_va": "Checkbox_77",
        "appraisal": "Checkbox_78",
        "short_sale": "Checkbox_79",
        "homeowners_insurance": "Checkbox_80",
        "firpta": "Checkbox_81",
        "interest_bearing": "Checkbox_82",
        "defective_drywall": "Checkbox_83",
        "coastal_construction": "Checkbox_84",
        "insulation": "Checkbox_85",
        "lead_paint": "Checkbox_86",
        "older_persons": "Checkbox_87",
        "rezoning": "Checkbox_88",
        "lease_purchase": "Checkbox_89",
        "pre_closing_occupancy": "Checkbox_90",
        "post_closing_occupancy": "Checkbox_91",
        "sale_of_buyers_property": "Checkbox_92",
        "backup_contract": "Checkbox_93",
        "kick_out": "Checkbox_94",
        "seller_attorney": "Checkbox_95",
        "buyer_attorney": "Checkbox_96",
        "licensee_interest": "Checkbox_97",
        "arbitration": "Checkbox_98"
    },

    # === COUNTER OFFER/RESOLUTION (145-146) ===
    "resolution": {
        "counter_offer": "Checkbox_145",
        "rejection": "Checkbox_146"
    },

    # === ADDITIONAL TERMS ===
    "additional_terms1": "Text_22",
    "additional_terms2": "Text_23",
    "additional_terms3": "Text_24",

    # === INITIALS (147-180) ===
    "buyer_initial1": "Initials_1",
    "seller_initial1": "Initials_2",
    "buyer_initial2": "Initials_3",
    "seller_initial2": "Initials_4",
    # ... (all initials through Initials_36)
}

# Helper function to extract tax ID from combined field
def extract_tax_id(tax_field_value):
    """Extract just the tax ID portion from a combined field like 'Miami-Dade County, FL Tax ID: 12-3456-789-0123'"""
    if not tax_field_value:
        return ""
    
    # Look for pattern like "Tax ID: 12-3456-789-0123"
    match = re.search(r'Tax ID:\s*([0-9\-]+)', tax_field_value)
    if match:
        return match.group(1)
    
    # If no "Tax ID:" pattern, return the original value
    return tax_field_value

# Helper function to process form data and apply field mappings
def process_form_data(form_data):
    """Process incoming form data and map to PDF field names - handles nested mappings"""
    processed_data = {}

    for user_field, value in form_data.items():
        if user_field in FIELD_MAP:
            pdf_field = FIELD_MAP[user_field]

            # Handle nested dictionary mappings (for grouped checkboxes)
            if isinstance(pdf_field, dict):
                # This is a grouped field like financing_type, included_items, etc.
                if isinstance(value, dict):
                    # Value is a dictionary of sub-options
                    for sub_key, sub_value in value.items():
                        if sub_key in pdf_field:
                            actual_pdf_field = pdf_field[sub_key]
                            processed_data[actual_pdf_field] = sub_value
                            print(f"DEBUG: Mapped {user_field}.{sub_key} -> {actual_pdf_field} = '{sub_value}'")
                else:
                    # Value is a simple value, but field expects nested structure
                    print(f"WARNING: {user_field} expects nested structure but got simple value: {value}")
            else:
                # Simple direct mapping
                processed_data[pdf_field] = value
                print(f"DEBUG: Mapped {user_field} -> {pdf_field} = '{value}'")
        else:
            # If no mapping found, try direct field name (for backwards compatibility)
            processed_data[user_field] = value
            print(f"DEBUG: Direct field {user_field} = '{value}'")

    print(f"DEBUG: Processed {len(processed_data)} total fields")
    return processed_data

# Helper function to set PDF fields with proper type handling
def set_pdf_fields(template_path, field_data):
    """Fill PDF form fields with provided data"""
    try:
        template_pdf = PdfReader(template_path)

        # Process the form data through our mapping
        processed_data = process_form_data(field_data)

        print(f"DEBUG: Input data keys: {list(processed_data.keys())}")

        filled_fields = []

        for page in template_pdf.pages:
            if page.Annots:
                for annotation in page.Annots:
                    if annotation.Subtype == '/Widget' and annotation.T:
                        field_name = annotation.T[1:-1]  # Remove parentheses

                        # Try to find a value for this field
                        field_value = None

                        # Direct match with parentheses
                        if f"({field_name})" in processed_data:
                            field_value = processed_data[f"({field_name})"]
                            print(f"DEBUG: Direct match (with parens) ({field_name}) = '{field_value}'")
                        # Direct match without parentheses
                        elif field_name in processed_data:
                            field_value = processed_data[field_name]
                            print(f"DEBUG: Direct match (without parens) {field_name} = '{field_value}'")

                        if field_value is not None:
                            # Handle different field types
                            if annotation.FT == '/Tx':  # Text field
                                annotation.V = str(field_value)
                                annotation.AP = ''  # Clear appearance to force regeneration
                                print(f"DEBUG: Set text field ({field_name}) = '{field_value}'")
                            elif annotation.FT == '/Btn':  # Button/Checkbox field
                                # Handle checkbox values
                                if str(field_value).lower() in ['true', '1', 'yes', 'on', '/yes']:
                                    annotation.V = '/Yes'
                                    annotation.AS = '/Yes'
                                else:
                                    annotation.V = '/Off'
                                    annotation.AS = '/Off'
                                print(f"DEBUG: Set checkbox ({field_name}) = {annotation.V}")

                            filled_fields.append((f"({field_name})", str(field_value)))

        print(f"DEBUG: Filled {len(filled_fields)} fields")
        return template_pdf, filled_fields

    except Exception as e:
        print(f"ERROR in set_pdf_fields: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

# ===== MAIN API ENDPOINT =====
@app.route('/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Main endpoint for generating filled PDF forms
    Accepts JSON payload with friendly field names and returns filled PDF
    """
    try:
        # Get form data from request
        form_data = request.json
        if not form_data:
            return jsonify({"error": "No form data provided"}), 400

        print(f"DEBUG: Received form data: {form_data}")

        # Fill the PDF with the provided data
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, form_data)

        # Create output buffer
        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        print(f"DEBUG: PDF generated successfully, size: {len(output_buffer.getvalue())} bytes")

        # Return the filled PDF as a downloadable file
        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='filled_florida_contract.pdf'
        )

    except Exception as e:
        print(f"ERROR in generate_pdf: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

# ===== ADDITIONAL API ENDPOINTS =====

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "PDF Form Filler API is running"})

@app.route('/fields', methods=['GET'])
def get_available_fields():
    """Return list of available field mappings"""
    return jsonify({
        "available_fields": list(FIELD_MAP.keys()),
        "total_fields": len(FIELD_MAP),
        "description": "Use these field names in your JSON payload to /generate-pdf"
    })

@app.route('/test-sample', methods=['GET'])
def test_sample():
    """Generate a test PDF with sample data"""
    sample_data = {
        "buyerName": "John Smith (\"Buyer\")",
        "sellerName": "Sarah Johnson (\"Seller\")",
        "parties": "John Smith (\"Buyer\") and Sarah Johnson (\"Seller\")",
        "propertyDescription": "Beautiful 3-bedroom, 2-bathroom single-family home with pool and 2-car garage",
        "streetAddress": "123 Sunset Boulevard, Miami, FL 33101",
        "propertyTaxID": "Miami-Dade County, FL Tax ID: 12-3456-789-0123",
        "legalDescription1": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "legalDescription2": "according to the plat thereof recorded in Plat Book 45, Page 67, Public Records of Miami-Dade County, Florida",
        "purchasePrice": "$750,000.00",
        "initialDeposit": "$75,000.00",
        "buyerInitials": "JS",
        "sellerInitials": "SJ",
        "conventionalFinancing": "true",
        "fixedRate": "true"
    }

    try:
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, sample_data)

        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='sample_filled_contract.pdf'
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5002)
