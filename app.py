from flask import Flask, request, send_file, jsonify
from pdfrw import Pdf<PERSON>ead<PERSON>, PdfWriter, PageMerge, PdfDict
import os
import io
import json
import re

app = Flask(__name__)

# Path to your fillable PDF form template
PDF_TEMPLATE_PATH = 'as is contract fillable.pdf'

# Load PDF fields for debugging
def load_pdf_fields():
    try:
        with open('pdf_fields.json', 'r') as f:
            return json.load(f)
    except:
        return {}

PDF_FIELDS = load_pdf_fields()

# Function to add a new form field to the PDF
def add_new_field_to_pdf(template_pdf, field_name, x, y, width=100, height=20, page_num=0):
    """Add a new text field to the PDF at specified coordinates"""
    try:
        # Get the page
        page = template_pdf.pages[page_num]

        # Create a new text field annotation
        new_field = PdfDict()
        new_field.Type = '/Annot'
        new_field.Subtype = '/Widget'
        new_field.FT = '/Tx'  # Text field
        new_field.T = f'({field_name})'  # Field name
        new_field.V = ''  # Initial value
        new_field.Rect = [x, y, x + width, y + height]  # Field rectangle
        new_field.F = 4  # Print flag
        new_field.P = page  # Parent page

        # Add the field to the page annotations
        if page.Annots is None:
            page.Annots = []
        page.Annots.append(new_field)

        print(f"DEBUG: Added new field '{field_name}' at coordinates ({x}, {y})")
        return True

    except Exception as e:
        print(f"ERROR: Failed to add new field '{field_name}': {str(e)}")
        return False

# FINAL FIELD MAPPING (CLEANED + FUNCTIONAL)

FIELD_MAP = {
    # ==== BUYER / SELLER NAMES ====
    "buyerName": "PARTIES",  # Actual field name for buyer
    "sellerName": "THIS FORM HAS BEEN APPROVED BY THE FLORIDA REALTORS AND THE FLORIDA BAR",  # Misnamed field for seller

    # ==== PROPERTY INFO ====
    "propertyDescription": "PROPERTY DESCRIPTION",        # e.g. 3 bed / 2 bath with pool
    "streetAddress": "a Street address city zip",         # e.g. 123 Main St, City, ZIP
    "legalDescription1": "c Real Property The legal description is 1",
    "legalDescription2": "c Real Property The legal description is 2",

    # ==== COUNTY & TAX ID (COMBINED FIELDS - HANDLED IN LOGIC) ====
    "county": "COUNTY_TEMP",                # Logical use only, NOT to be filled directly
    "propertyTaxID": "TAX_ID_TEMP",         # Logical use only, NOT to be filled directly
    "countyTaxID": "countyTaxID",           # The field you'll programmatically add at (383, 172)

    # ==== CONTACT INFO ====
    "email": "Email",
    "fax": "Fax",
    "escrowAgentName": "Escrow Agent Information Name",
    "escrowAgentAddress": "Address",

    # ==== INITIALS ====
    "buyerInitials": "Buyers Initials",
    "buyerInitials2": "Buyers Initials_2",
    "sellerInitials": "Sellers Initials",
    "sellerInitials2": "Sellers Initials_2",
    "sellerInitials3": "Sellers Initials_3",

    # ==== DATES ====
    "closingDate": "Closing Date at the time established by the Closing Agent",
    "contractDate": "Date",
    "effectiveDate": "Date_2",

    # ==== FINANCIAL INFO ====
    "purchasePrice": "Text79",
    "loanAmount": "Text88",
    "downPayment": "Text89",
    "monthlyPayment": "Text91",
    "finalAmount": "Text103",

    # ==== ADDRESS FOR NOTICE ====
    "buyerAddress1": "Buyers address for purposes of notice 1",
    "buyerAddress2": "Buyers address for purposes of notice 2",
    "buyerAddress3": "Buyers address for purposes of notice 3",
    "sellerAddress1": "Sellers address for purposes of notice 1",
    "sellerAddress2": "Sellers address for purposes of notice 2",
    "sellerAddress3": "Sellers address for purposes of notice 3",

    # ==== REALTOR INFO ====
    "listingBroker": "Listing Broker",
    "listingSalesAssociate": "Listing Sales Associate",
    "cooperatingBroker": "Cooperating Broker if any",
    "cooperatingSalesAssociate": "Cooperating Sales Associate if any",

    # ==== PROPERTY FEATURES ====
    "includedPersonalProperty": "Personal Property is included in the Purchase Price has no contributory value and shall be left for the Buyer",
    "excludedItems": "e The following items are excluded from the purchase",
    "otherPersonalProperty": "Other Personal Property items included in this purchase are",
    "accessDevices": "and other access devices and storm shutterspanels Personal Property",

    # ==== SPECIAL / MISNAMED ====
    "special_seller_name": "THIS FORM HAS BEEN APPROVED BY THE FLORIDA REALTORS AND THE FLORIDA BAR",
    "special_property_tax_id": "undefined_2"
}


# Helper function to extract tax ID from combined field
def extract_tax_id(tax_field_value):
    """Extract just the tax ID portion from a combined field like 'Miami-Dade County, FL Tax ID: 12-3456-789-0123'"""
    if not tax_field_value:
        return ""
    
    # Look for pattern like "Tax ID: 12-3456-789-0123"
    match = re.search(r'Tax ID:\s*([0-9\-]+)', tax_field_value)
    if match:
        return match.group(1)
    
    # If no "Tax ID:" pattern, return the original value
    return tax_field_value

# Helper function to process form data and apply field mappings
def process_form_data(form_data):
    """Process incoming form data and map to PDF field names"""
    processed_data = {}

    # Temporary storage for combination fields
    buyer_name = None
    seller_name = None
    county = None
    tax_id = None
    county_tax_id_new = None

    # First pass: collect individual values and process regular fields
    for user_field, value in form_data.items():
        if user_field in FIELD_MAP:
            pdf_field = FIELD_MAP[user_field]

            # Handle special combination fields
            if pdf_field == "BUYER_NAME_TEMP":
                buyer_name = str(value) if value else ""
                continue
            elif pdf_field == "SELLER_NAME_TEMP":
                seller_name = str(value) if value else ""
                continue
            elif pdf_field == "COUNTY_TEMP":
                county = str(value) if value else ""
                continue
            elif pdf_field == "TAX_ID_TEMP":
                tax_id = str(value) if value else ""
                continue
            elif pdf_field == "COUNTY_TAX_ID_NEW":
                county_tax_id_new = str(value) if value else ""
                continue
            

            # Regular field processing
            processed_data[pdf_field] = value
        else:
            # If no mapping found, try direct field name (for backwards compatibility)
            processed_data[user_field] = value

    # Second pass: Create combination fields

    # Combine buyer and seller names into PARTIES field
    if buyer_name or seller_name:
        if buyer_name and seller_name:
            parties_text = f'{buyer_name} (\\"Buyer\\") and {seller_name} (\\"Seller\\")'
        elif buyer_name:
            parties_text = f'{buyer_name} (\\"Buyer\\")'
        elif seller_name:
            parties_text = f'{seller_name} (\\"Seller\\")'
        else:
            parties_text = ""

        processed_data["(PARTIES)"] = parties_text
        print(f"DEBUG: Combined PARTIES field: '{parties_text}'")

    # Combine county and tax ID into County Florida Property Tax ID field
    if county or tax_id:
        if county and tax_id:
            # Clean the tax ID if it contains extra text
            clean_tax_id = extract_tax_id(tax_id)
            # Remove "County" suffix if it exists to avoid duplication
            clean_county = county.replace(" County", "").replace(" county", "")
            county_tax_text = f"{clean_county} County, Florida. Property Tax ID #: {clean_tax_id}"
        elif county:
            clean_county = county.replace(" County", "").replace(" county", "")
            county_tax_text = f"{clean_county} County, Florida. Property Tax ID #: "
        elif tax_id:
            clean_tax_id = extract_tax_id(tax_id)
            county_tax_text = f"County, Florida. Property Tax ID #: {clean_tax_id}"
        else:
            county_tax_text = ""

        processed_data["(County Florida Property Tax ID)"] = county_tax_text
        processed_data["countyTaxID"] = county_tax_text  # <-- This makes the new custom field fill

        print(f"DEBUG: Combined County/Tax ID field: '{county_tax_text}'")

    # Handle new countyTaxID field (will be added programmatically to PDF)
    if county_tax_id_new:
        processed_data["countyTaxID"] = county_tax_id_new
        print(f"DEBUG: New County Tax ID field: '{county_tax_id_new}'")

    return processed_data

# Helper function to set PDF fields with proper type handling
def set_pdf_fields(template_path, field_data):
    """Fill PDF form fields with provided data"""
    try:
        template_pdf = PdfReader(template_path)

        # Add the new countyTaxID field if it doesn't exist
        add_new_field_to_pdf(template_pdf, 'countyTaxID', 383, 172, width=150, height=15, page_num=0)

        # Process the form data through our mapping
        processed_data = process_form_data(field_data)

        print(f"DEBUG: Input data keys: {list(processed_data.keys())}")

        filled_fields = []

        for page in template_pdf.pages:
            if page.Annots:
                for annotation in page.Annots:
                    if annotation.Subtype == '/Widget' and annotation.T:
                        field_name = annotation.T[1:-1]  # Remove parentheses

                        # Try to find a value for this field
                        field_value = None

                        # Direct match with parentheses
                        if f"({field_name})" in processed_data:
                            field_value = processed_data[f"({field_name})"]
                            print(f"DEBUG: Direct match (with parens) ({field_name}) = '{field_value}'")
                        # Direct match without parentheses
                        elif field_name in processed_data:
                            field_value = processed_data[field_name]
                            print(f"DEBUG: Direct match (without parens) {field_name} = '{field_value}'")
                        # Special handling for new countyTaxID field
                        elif field_name == 'countyTaxID' and 'countyTaxID' in processed_data:
                            field_value = processed_data['countyTaxID']
                            print(f"DEBUG: New field match {field_name} = '{field_value}'")

                        if field_value is not None:
                            # Handle different field types
                            if annotation.FT == '/Tx':  # Text field
                                annotation.V = str(field_value)
                                annotation.AP = ''  # Clear appearance to force regeneration
                                print(f"DEBUG: Set text field ({field_name}) = '{field_value}'")
                            elif annotation.FT == '/Btn':  # Button/Checkbox field
                                # Handle checkbox values
                                if str(field_value).lower() in ['true', '1', 'yes', 'on', '/yes']:
                                    annotation.V = '/Yes'
                                    annotation.AS = '/Yes'
                                else:
                                    annotation.V = '/Off'
                                    annotation.AS = '/Off'
                                print(f"DEBUG: Set checkbox ({field_name}) = {annotation.V}")

                            filled_fields.append((f"({field_name})", str(field_value)))

        print(f"DEBUG: Filled {len(filled_fields)} fields")
        return template_pdf, filled_fields

    except Exception as e:
        print(f"ERROR in set_pdf_fields: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

# ===== MAIN API ENDPOINT =====
@app.route('/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Main endpoint for generating filled PDF forms
    Accepts JSON payload with friendly field names and returns filled PDF
    """
    try:
        # Get form data from request
        form_data = request.json
        if not form_data:
            return jsonify({"error": "No form data provided"}), 400

        print(f"DEBUG: Received form data: {form_data}")

        # Fill the PDF with the provided data
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, form_data)

        # Create output buffer
        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        print(f"DEBUG: PDF generated successfully, size: {len(output_buffer.getvalue())} bytes")

        # Return the filled PDF as a downloadable file
        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='filled_florida_contract.pdf'
        )

    except Exception as e:
        print(f"ERROR in generate_pdf: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

# ===== ADDITIONAL API ENDPOINTS =====

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "PDF Form Filler API is running"})

@app.route('/fields', methods=['GET'])
def get_available_fields():
    """Return list of available field mappings"""
    return jsonify({
        "available_fields": list(FIELD_MAP.keys()),
        "total_fields": len(FIELD_MAP),
        "description": "Use these field names in your JSON payload to /generate-pdf"
    })

@app.route('/test-sample', methods=['GET'])
def test_sample():
    """Generate a test PDF with sample data"""
    sample_data = {
        "buyerName": "John Smith (\"Buyer\")",
        "sellerName": "Sarah Johnson (\"Seller\")",
        "parties": "John Smith (\"Buyer\") and Sarah Johnson (\"Seller\")",
        "propertyDescription": "Beautiful 3-bedroom, 2-bathroom single-family home with pool and 2-car garage",
        "streetAddress": "123 Sunset Boulevard, Miami, FL 33101",
        "propertyTaxID": "Miami-Dade County, FL Tax ID: 12-3456-789-0123",
        "legalDescription1": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "legalDescription2": "according to the plat thereof recorded in Plat Book 45, Page 67, Public Records of Miami-Dade County, Florida",
        "purchasePrice": "$750,000.00",
        "initialDeposit": "$75,000.00",
        "buyerInitials": "JS",
        "sellerInitials": "SJ",
        "conventionalFinancing": "true",
        "fixedRate": "true"
    }

    try:
        filled_pdf, _ = set_pdf_fields(PDF_TEMPLATE_PATH, sample_data)

        output_buffer = io.BytesIO()
        PdfWriter().write(output_buffer, filled_pdf)
        output_buffer.seek(0)

        return send_file(
            output_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name='sample_filled_contract.pdf'
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5002)
