# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files and outputs
test_*.pdf
debug_*.pdf
filled_*.pdf
comprehensive_*.pdf
corrected_*.pdf
final_*.pdf
fixed_*.pdf
field_mapping_*.pdf
streamlit_*.pdf
*.pdf
!Florida-As_Is-Purchase-Agreement.pdf

# Test scripts
*.sh
comprehensive_mock_data.json
test_*.py
debug_*.py
create_*.py
pdf_analyzer.py

# Logs
*.log
logs/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
