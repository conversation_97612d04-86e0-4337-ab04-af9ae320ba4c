#!/usr/bin/env python3

from pdfrw import PdfReader
import json

def extract_pdf_fields(pdf_path):
    """Extract all field names from a PDF"""
    template_pdf = PdfReader(pdf_path)
    fields = {}
    
    for page_num, page in enumerate(template_pdf.pages, 1):
        if page.Annots:
            for annotation in page.Annots:
                if annotation.Subtype == '/Widget' and annotation.T:
                    # Safely extract field name
                    field_name_raw = annotation.T
                    
                    # Handle different field name formats
                    try:
                        if hasattr(field_name_raw, 'decode'):
                            field_name = field_name_raw.decode('utf-8')
                        else:
                            field_name = str(field_name_raw)
                            
                        # Remove parentheses if present
                        if field_name.startswith('(') and field_name.endswith(')'):
                            field_name = field_name[1:-1]
                    except:
                        field_name = repr(field_name_raw)

                    # Safely get field type and value
                    try:
                        field_type = str(annotation['/FT']) if '/FT' in annotation else 'Unknown'
                    except:
                        field_type = 'Unknown'

                    try:
                        field_value = str(annotation['/V']) if '/V' in annotation else ''
                    except:
                        field_value = ''

                    field_info = {
                        "name": field_name,
                        "type": field_type,
                        "current_value": field_value,
                        "page": page_num
                    }
                    fields[field_name] = field_info
    
    return fields

if __name__ == "__main__":
    pdf_path = "as is contract fillable.pdf"
    fields = extract_pdf_fields(pdf_path)
    
    print(f"Found {len(fields)} fields:")
    for field_name, field_info in fields.items():
        print(f"  '{field_name}' (page {field_info['page']}, type: {field_info['type']})")
    
    # Save to JSON file
    with open('pdf_fields.json', 'w') as f:
        json.dump(fields, f, indent=2)
    
    print(f"\nField information saved to pdf_fields.json")
