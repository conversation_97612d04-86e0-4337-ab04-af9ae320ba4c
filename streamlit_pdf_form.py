#!/usr/bin/env python3

import streamlit as st
import requests
import json
from datetime import datetime
import base64

# Configure Streamlit page
st.set_page_config(
    page_title="Florida Real Estate Contract Generator",
    page_icon="🏠",
    layout="wide"
)

# API endpoints
API_URL_V4 = "http://localhost:5002/fill_pdf_v4"
API_URL_V5 = "http://localhost:5002/fill_pdf_v5"

# Mock data for quick testing
MOCK_DATA = {
    "FIELD-PARTIES": "<PERSON> (\"Buyer\") and <PERSON> (\"Seller\")",
    "FIELD-PROPERTY-DESC": "Beautiful 3-bedroom, 2-bathroom single-family home with pool and 2-car garage",
    "FIELD-STREET-ADDRESS": "123 Sunset Boulevard, Miami, FL 33101",
    "FIELD-COUNTY-TAX-ID": "Miami-Dade County, FL Tax ID: 12-3456-789-0123",
    "FIELD-LEGAL-DESC-1": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
    "FIELD-LEGAL-DESC-2": "according to the plat thereof recorded in Plat Book 45, Page 67, Public Records of Miami-Dade County, Florida"
}

def generate_pdf(form_data, use_v5=False):
    """Generate PDF using the API"""
    try:
        api_url = API_URL_V5 if use_v5 else API_URL_V4
        version = "v5" if use_v5 else "v4"
        st.write(f"🔄 Sending request to PDF service ({version})...")
        response = requests.post(api_url, json=form_data, timeout=30)

        if response.status_code == 200:
            pdf_size = len(response.content)
            st.write(f"✅ PDF generated successfully! Size: {pdf_size:,} bytes")

            # Verify it's actually a PDF
            if response.content.startswith(b'%PDF'):
                st.write("✅ Valid PDF format confirmed")
                return response.content
            else:
                st.error("❌ Response is not a valid PDF file")
                st.write(f"Response content preview: {response.content[:100]}")
                return None
        else:
            st.error(f"❌ API Error: {response.status_code}")
            st.write(f"Response: {response.text}")
            return None

    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to the PDF generation service. Please ensure the Flask app is running on port 5001.")
        return None
    except requests.exceptions.Timeout:
        st.error("❌ Request timed out. The PDF generation is taking too long.")
        return None
    except Exception as e:
        st.error(f"❌ Unexpected error: {str(e)}")
        return None

def main():
    st.title("🏠 Florida Real Estate Contract Generator")
    st.markdown("Generate filled Florida \"AS IS\" Residential Contract For Sale And Purchase")
    
    # Sidebar for quick actions
    with st.sidebar:
        st.header("Quick Actions")

        if st.button("🎯 Fill with Mock Data", type="primary"):
            for key, value in MOCK_DATA.items():
                st.session_state[key] = value
            st.success("✅ Mock data loaded!")
            st.rerun()

        if st.button("🗑️ Clear All Fields"):
            for key in MOCK_DATA.keys():
                if key in st.session_state:
                    del st.session_state[key]
            st.success("✅ All fields cleared!")
            st.rerun()

        st.divider()

        if st.button("🧪 Test All Fields", type="secondary"):
            # Download the comprehensive test PDF directly
            try:
                response = requests.get("http://localhost:5001/test_all_fields")
                if response.status_code == 200:
                    st.download_button(
                        label="📄 Download Comprehensive Test PDF",
                        data=response.content,
                        file_name="comprehensive_test_all_fields.pdf",
                        mime="application/pdf",
                        type="primary"
                    )
                    st.success("✅ Comprehensive test PDF generated! Click the download button above.")
                else:
                    st.error(f"❌ Error generating comprehensive test: {response.status_code}")
            except Exception as e:
                st.error(f"❌ Error: {str(e)}")

        st.markdown("---")
        st.header("🔧 Debug")

        if st.button("🧪 Test API Connection"):
            try:
                response = requests.get("http://localhost:5001/health", timeout=5)
                if response.status_code == 200:
                    st.success("✅ API connection working!")
                else:
                    st.error(f"❌ API returned: {response.status_code}")
            except:
                st.error("❌ Cannot connect to API on port 5001")

        if st.button("🔬 Test V5 Method"):
            test_data = {
                "FIELD-PARTIES": "TEST V5: John Doe",
                "FIELD-PROPERTY-DESC": "TEST V5: Sample property",
                "FIELD-STREET-ADDRESS": "TEST V5: 123 Test St"
            }
            with st.spinner("Testing V5 method..."):
                pdf_content = generate_pdf(test_data, use_v5=True)
                if pdf_content:
                    st.download_button(
                        "📥 Download V5 Test PDF",
                        data=pdf_content,
                        file_name="test_v5.pdf",
                        mime="application/pdf"
                    )
    
    # Main form
    with st.form("contract_form"):
        st.header("📋 Contract Information")
        
        # Create two columns for better layout
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("👥 Parties")
            st.text_area(
                "Buyer and Seller Information",
                value=st.session_state.get("FIELD-PARTIES", ""),
                help="Enter buyer and seller names, e.g., 'John Smith (\"Buyer\") and Jane Doe (\"Seller\")'",
                height=100,
                key="FIELD-PARTIES"
            )

            st.subheader("🏡 Property Description")
            st.text_area(
                "Property Description",
                value=st.session_state.get("FIELD-PROPERTY-DESC", ""),
                help="Brief description of the property",
                height=100,
                key="FIELD-PROPERTY-DESC"
            )

            st.subheader("📍 Property Address")
            st.text_input(
                "Street Address, City, State, ZIP",
                value=st.session_state.get("FIELD-STREET-ADDRESS", ""),
                help="Complete property address",
                key="FIELD-STREET-ADDRESS"
            )

        with col2:
            st.subheader("🏛️ County & Tax Information")
            st.text_input(
                "County and Tax ID",
                value=st.session_state.get("FIELD-COUNTY-TAX-ID", ""),
                help="Format: 'County Name, State Tax ID: XX-XXXX-XXX-XXXX'",
                key="FIELD-COUNTY-TAX-ID"
            )

            st.subheader("📜 Legal Description")
            st.text_input(
                "Legal Description Line 1",
                value=st.session_state.get("FIELD-LEGAL-DESC-1", ""),
                help="First line of legal property description",
                key="FIELD-LEGAL-DESC-1"
            )

            st.text_area(
                "Legal Description Line 2",
                value=st.session_state.get("FIELD-LEGAL-DESC-2", ""),
                help="Second line of legal property description",
                height=100,
                key="FIELD-LEGAL-DESC-2"
            )
        
        # Generate button
        st.markdown("---")

        # Method selection
        col_v4, col_v5 = st.columns(2)
        with col_v4:
            submitted_v4 = st.form_submit_button("🚀 Generate PDF (v4)", type="primary", use_container_width=True)
        with col_v5:
            submitted_v5 = st.form_submit_button("🔬 Generate PDF (v5)", type="secondary", use_container_width=True)

    # Handle form submission outside the form
    if submitted_v4 or submitted_v5:
        use_v5 = submitted_v5
        version = "v5" if use_v5 else "v4"

        # Get values from session state (form variables)
        required_fields = {
            "FIELD-PARTIES": st.session_state.get("FIELD-PARTIES", ""),
            "FIELD-PROPERTY-DESC": st.session_state.get("FIELD-PROPERTY-DESC", ""),
            "FIELD-STREET-ADDRESS": st.session_state.get("FIELD-STREET-ADDRESS", ""),
            "FIELD-COUNTY-TAX-ID": st.session_state.get("FIELD-COUNTY-TAX-ID", ""),
            "FIELD-LEGAL-DESC-1": st.session_state.get("FIELD-LEGAL-DESC-1", ""),
            "FIELD-LEGAL-DESC-2": st.session_state.get("FIELD-LEGAL-DESC-2", "")
        }

        # Check if any required fields are empty
        empty_fields = [field for field, value in required_fields.items() if not value.strip()]

        if empty_fields:
            st.error(f"❌ Please fill in all required fields: {', '.join(empty_fields)}")
        else:
            # Show progress
            with st.spinner(f"🔄 Generating PDF contract using method {version}..."):
                pdf_content = generate_pdf(required_fields, use_v5=use_v5)

            if pdf_content:
                # Store PDF in session state for download
                st.session_state.pdf_content = pdf_content
                st.session_state.pdf_generated = True
                st.session_state.contract_data = required_fields
                st.session_state.pdf_version = version

                # Success message
                st.success(f"✅ PDF contract generated successfully using method {version}!")

    # Download section (outside form)
    if st.session_state.get('pdf_generated', False):
        st.markdown("---")
        version = st.session_state.get('pdf_version', 'unknown')
        st.header(f"📥 Download Your Contract (Generated with {version})")

        # Create download button
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Florida_Contract_{version}_{timestamp}.pdf"

        st.download_button(
            label="📥 Download PDF Contract",
            data=st.session_state.pdf_content,
            file_name=filename,
            mime="application/pdf",
            type="primary",
            use_container_width=True
        )

        # Show summary
        with st.expander("📋 Contract Summary", expanded=True):
            contract_data = st.session_state.contract_data
            st.write("**Parties:**", contract_data["FIELD-PARTIES"])
            st.write("**Property:**", contract_data["FIELD-PROPERTY-DESC"])
            st.write("**Address:**", contract_data["FIELD-STREET-ADDRESS"])
            st.write("**County/Tax ID:**", contract_data["FIELD-COUNTY-TAX-ID"])
            st.write("**Legal Description:**")
            st.write(f"- {contract_data['FIELD-LEGAL-DESC-1']}")
            st.write(f"- {contract_data['FIELD-LEGAL-DESC-2']}")

        # Clear button
        if st.button("🗑️ Generate New Contract", type="secondary"):
            st.session_state.pdf_generated = False
            if 'pdf_content' in st.session_state:
                del st.session_state.pdf_content
            if 'contract_data' in st.session_state:
                del st.session_state.contract_data
            st.rerun()

    # Instructions section
    with st.expander("ℹ️ Instructions & Tips"):
        st.markdown("""
        ### How to Use:
        1. **Quick Start**: Click "Fill with Mock Data" in the sidebar for sample data
        2. **Fill Forms**: Enter all required contract information
        3. **Generate**: Click "Generate PDF Contract" to create the filled document
        4. **Download**: Use the download button to save your PDF
        
        ### Field Guidelines:
        - **Parties**: Include both buyer and seller with clear designation
        - **Property Description**: Brief but descriptive summary of the property
        - **Address**: Complete street address including city, state, and ZIP
        - **County/Tax ID**: Format as "County Name, State Tax ID: XX-XXXX-XXX-XXXX"
        - **Legal Description**: Official legal description from property records
        
        ### Requirements:
        - Flask PDF service must be running on port 5001
        - All fields are required for PDF generation
        """)

if __name__ == "__main__":
    main()
