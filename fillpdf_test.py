#!/usr/bin/env python3
"""
Test using fillpdf library for better checkbox handling
"""

from fillpdf import fillpdfs
import json

def test_fillpdf_checkboxes():
    """Test checkbox filling using fillpdf library"""
    
    # Test data with checkboxes
    test_data = {
        "Text_1": "<PERSON>",
        "Text_2": "<PERSON>", 
        "Text_3": "123 Ocean Drive, Miami Beach, FL 33139",
        "Text_4": "Miami-Dade",
        "Text_5": "12-3456-789-0123", 
        "Text_6": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "Number_1": "750000",
        "Number_2": "75000",
        "Date_1": "2025-07-15",
        
        # Checkboxes - try different values
        "Checkbox_8": "Yes",    # Cash
        "Checkbox_9": "Yes",    # Conventional
        "Checkbox_16": "Yes",   # Refrigerator
        "Checkbox_17": "Yes",   # Washer
        "Checkbox_18": "Yes",   # Dryer
    }
    
    try:
        print("🔍 Testing fillpdf library...")
        
        # Fill the PDF using fillpdf
        fillpdfs.write_fillable_pdf(
            'as is contract fillable.pdf',
            'fillpdf_test_output.pdf',
            test_data
        )
        
        print("✅ fillpdf test completed!")
        print("📁 Output: fillpdf_test_output.pdf")
        
    except Exception as e:
        print(f"❌ fillpdf error: {e}")
        
        # Try with different checkbox values
        print("\n🔄 Trying with different checkbox values...")
        test_data_alt = test_data.copy()
        
        # Try boolean values
        for key in test_data_alt:
            if key.startswith('Checkbox_'):
                test_data_alt[key] = True
        
        try:
            fillpdfs.write_fillable_pdf(
                'as is contract fillable.pdf',
                'fillpdf_test_bool.pdf',
                test_data_alt
            )
            print("✅ Boolean checkbox test completed!")
            print("📁 Output: fillpdf_test_bool.pdf")
        except Exception as e2:
            print(f"❌ Boolean test error: {e2}")
            
            # Try with "On" values
            print("\n🔄 Trying with 'On' values...")
            test_data_on = test_data.copy()
            for key in test_data_on:
                if key.startswith('Checkbox_'):
                    test_data_on[key] = "On"
            
            try:
                fillpdfs.write_fillable_pdf(
                    'as is contract fillable.pdf',
                    'fillpdf_test_on.pdf',
                    test_data_on
                )
                print("✅ 'On' checkbox test completed!")
                print("📁 Output: fillpdf_test_on.pdf")
            except Exception as e3:
                print(f"❌ 'On' test error: {e3}")

def get_field_info():
    """Get information about PDF fields"""
    try:
        print("\n📋 Getting PDF field information...")
        fields = fillpdfs.get_form_fields('as is contract fillable.pdf')
        
        print(f"📊 Found {len(fields)} fields")
        
        # Look for checkbox fields
        checkbox_fields = {}
        for field_name, field_info in fields.items():
            if 'Checkbox' in field_name:
                checkbox_fields[field_name] = field_info
                print(f"🔲 {field_name}: {field_info}")
        
        # Save field info to file
        with open('fillpdf_field_info.json', 'w') as f:
            json.dump(fields, f, indent=2)
        
        print("📁 Field info saved to: fillpdf_field_info.json")
        
        return checkbox_fields
        
    except Exception as e:
        print(f"❌ Field info error: {e}")
        return {}

if __name__ == "__main__":
    # First get field information
    checkbox_info = get_field_info()
    
    # Then test filling
    test_fillpdf_checkboxes()
