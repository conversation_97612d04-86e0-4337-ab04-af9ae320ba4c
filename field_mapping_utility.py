#!/usr/bin/env python3
"""
Field Mapping Utility for PDF Forms

This utility helps detect and fix weird field names automatically 
from their page coordinates or visual labels.
"""

import json
import re
from pdfrw import PdfReader
from typing import Dict, List, Tuple, Optional

class FieldMappingAnalyzer:
    """Analyzes PDF fields and suggests better mappings based on context"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.pdf_reader = PdfReader(pdf_path)
        self.fields = self.extract_fields_with_coordinates()
        
    def extract_fields_with_coordinates(self) -> Dict[str, Dict]:
        """Extract all fields with their coordinates and metadata"""
        fields = {}
        
        for page_num, page in enumerate(self.pdf_reader.pages):
            if page.Annots:
                for annotation in page.Annots:
                    if annotation.Subtype == '/Widget' and annotation.T:
                        field_name = str(annotation.T)[1:-1]  # Remove quotes
                        
                        # Extract coordinates
                        rect = annotation.Rect if annotation.Rect else [0, 0, 0, 0]
                        x1, y1, x2, y2 = [float(coord) for coord in rect]
                        
                        field_info = {
                            'name': field_name,
                            'page': page_num + 1,
                            'coordinates': {
                                'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                                'center_x': (x1 + x2) / 2,
                                'center_y': (y1 + y2) / 2,
                                'width': x2 - x1,
                                'height': y2 - y1
                            },
                            'type': str(annotation.FT) if annotation.FT else 'Unknown',
                            'value': str(annotation.V) if annotation.V else ''
                        }
                        
                        fields[field_name] = field_info
        
        return fields
    
    def analyze_field_purpose(self, field_name: str) -> Dict[str, str]:
        """Analyze what a field is likely used for based on its name"""
        field_name_lower = field_name.lower()
        
        # Define patterns for different field types
        patterns = {
            'parties': [
                r'parties', r'buyer.*seller', r'purchaser.*vendor'
            ],
            'buyer_name': [
                r'buyer(?!.*seller)', r'purchaser', r'vendee'
            ],
            'seller_name': [
                r'seller(?!.*buyer)', r'vendor', r'grantor'
            ],
            'property_description': [
                r'property.*description', r'description.*property', 
                r'real.*property.*description'
            ],
            'street_address': [
                r'street.*address', r'address.*street', r'property.*address',
                r'street.*city.*zip', r'address.*city.*zip'
            ],
            'county_tax_id': [
                r'county.*tax.*id', r'tax.*id.*county', r'property.*tax.*id',
                r'county.*florida.*tax'
            ],
            'legal_description': [
                r'legal.*description', r'description.*legal', r'real.*property.*legal'
            ],
            'purchase_price': [
                r'purchase.*price', r'price.*purchase', r'sale.*price'
            ],
            'deposit': [
                r'deposit', r'earnest.*money', r'down.*payment'
            ],
            'initials': [
                r'initials', r'buyer.*initials', r'seller.*initials'
            ],
            'date': [
                r'date', r'closing.*date', r'contract.*date'
            ],
            'checkbox': [
                r'check', r'box', r'yes.*no', r'true.*false'
            ]
        }
        
        suggestions = []
        confidence_scores = {}
        
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                if re.search(pattern, field_name_lower):
                    suggestions.append(category)
                    confidence_scores[category] = confidence_scores.get(category, 0) + 1
        
        # Sort by confidence
        best_match = max(confidence_scores.items(), key=lambda x: x[1]) if confidence_scores else ('unknown', 0)
        
        return {
            'suggested_purpose': best_match[0],
            'confidence': best_match[1],
            'all_suggestions': suggestions
        }
    
    def detect_weird_mappings(self) -> List[Dict]:
        """Detect fields with potentially weird or misleading names"""
        weird_fields = []
        
        for field_name, field_info in self.fields.items():
            analysis = self.analyze_field_purpose(field_name)
            
            # Check for obviously weird mappings
            is_weird = False
            reason = ""
            
            # Check for misleading names
            if 'florida realtors' in field_name.lower() and analysis['suggested_purpose'] != 'header':
                is_weird = True
                reason = "Field name suggests header/title but may be used for data input"
            
            elif 'property description' in field_name.lower() and field_info['coordinates']['center_y'] > 700:
                # If "property description" field is very high on page, might be address field
                is_weird = True
                reason = "Property description field positioned like an address field"
            
            elif 'parties' in field_name.lower() and field_info['coordinates']['width'] < 100:
                is_weird = True
                reason = "Parties field seems too narrow for full buyer/seller names"
            
            elif len(field_name) > 50:
                is_weird = True
                reason = "Field name is unusually long and descriptive"
            
            elif analysis['suggested_purpose'] == 'unknown' and field_info['type'] == '/Tx':
                is_weird = True
                reason = "Cannot determine field purpose from name"
            
            if is_weird:
                weird_fields.append({
                    'field_name': field_name,
                    'reason': reason,
                    'analysis': analysis,
                    'coordinates': field_info['coordinates'],
                    'page': field_info['page'],
                    'suggested_mapping': self.suggest_better_mapping(field_name, field_info)
                })
        
        return weird_fields
    
    def suggest_better_mapping(self, field_name: str, field_info: Dict) -> str:
        """Suggest a better mapping name for a field"""
        analysis = self.analyze_field_purpose(field_name)
        coords = field_info['coordinates']
        
        # Use position-based heuristics
        if coords['center_y'] > 750:  # Top of page
            if coords['center_x'] < 300:
                return "buyer_name"
            else:
                return "seller_name"
        elif coords['center_y'] > 650:  # Upper middle
            return "property_description"
        elif coords['center_y'] > 550:  # Middle
            if 'address' in field_name.lower() or coords['width'] > 200:
                return "street_address"
            else:
                return "county_tax_id"
        elif coords['center_y'] > 450:  # Lower middle
            return "legal_description_1"
        else:  # Bottom
            return "legal_description_2"
    
    def generate_corrected_field_map(self) -> Dict[str, str]:
        """Generate a corrected FIELD_MAP dictionary"""
        corrected_map = {}
        
        for field_name, field_info in self.fields.items():
            suggested_mapping = self.suggest_better_mapping(field_name, field_info)
            corrected_map[suggested_mapping] = f"({field_name})"
        
        return corrected_map
    
    def save_analysis_report(self, output_file: str = "field_analysis_report.json"):
        """Save detailed analysis report"""
        weird_fields = self.detect_weird_mappings()
        corrected_map = self.generate_corrected_field_map()
        
        report = {
            'pdf_file': self.pdf_path,
            'total_fields': len(self.fields),
            'weird_fields_count': len(weird_fields),
            'weird_fields': weird_fields,
            'all_fields': self.fields,
            'suggested_field_map': corrected_map,
            'recommendations': [
                "Review fields with weird mappings",
                "Test suggested mappings with sample data",
                "Consider field positioning when mapping",
                "Verify field purposes match their intended use"
            ]
        }
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Analysis report saved to {output_file}")
        return report

def main():
    """Main function to run the field mapping analysis"""
    pdf_path = "Florida-As_Is-Purchase-Agreement.pdf"
    
    print("🔍 Analyzing PDF field mappings...")
    analyzer = FieldMappingAnalyzer(pdf_path)
    
    print(f"📄 Found {len(analyzer.fields)} fields in PDF")
    
    # Detect weird mappings
    weird_fields = analyzer.detect_weird_mappings()
    print(f"⚠️  Found {len(weird_fields)} potentially problematic field mappings:")
    
    for field in weird_fields:
        print(f"  • {field['field_name'][:50]}...")
        print(f"    Reason: {field['reason']}")
        print(f"    Suggested: {field['suggested_mapping']}")
        print()
    
    # Generate corrected field map
    corrected_map = analyzer.generate_corrected_field_map()
    print("✅ Generated corrected FIELD_MAP:")
    print("FIELD_MAP = {")
    for logical_name, pdf_field in corrected_map.items():
        print(f'    "{logical_name}": "{pdf_field}",')
    print("}")
    
    # Save detailed report
    analyzer.save_analysis_report()

if __name__ == "__main__":
    main()
