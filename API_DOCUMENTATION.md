# PDF Form Filler API Documentation

## Overview

This Flask API auto-fills Florida "AS IS" Residential Contract For Sale And Purchase PDF forms using friendly field names that map to the actual PDF form fields.

## Base URL
```
http://localhost:5002
```

## Main Endpoint

### POST `/generate-pdf`

**Description**: Generate a filled PDF contract using friendly field names.

**Content-Type**: `application/json`

**Request Body**: JSON object with friendly field names and their values.

**Response**: PDF file download (`application/pdf`)

#### Example Request

```bash
curl -X POST http://localhost:5002/generate-pdf \
  -H "Content-Type: application/json" \
  -d '{
    "buyerName": "<PERSON>",
    "sellerName": "Sarah Johnson",
    "county": "Miami-Dade",
    "propertyTaxID": "12-3456-789-0123",
    "propertyDescription": "Beautiful 3-bedroom home with pool",
    "streetAddress": "123 Main Street, Miami, FL 33101",
    "legalDescription1": "Lot 15, Block 3, SUNSET ESTATES",
    "legalDescription2": "according to plat book 45, page 67",
    "purchasePrice": "$750,000.00",
    "initialDeposit": "$75,000.00",
    "buyerInitials": "JS",
    "sellerInitials": "SJ",
    "conventionalFinancing": "true",
    "fixedRate": "true"
  }' \
  --output filled_contract.pdf
```

**Note**: The API automatically combines:
- `buyerName` + `sellerName` → `"<PERSON> (\"Buyer\") and Sarah Johnson (\"Seller\")"` in the PARTIES field
- `county` + `propertyTaxID` → `"Miami-Dade County, Florida. Property Tax ID #: 12-3456-789-0123"` in the County Florida Property Tax ID field

**Smart Processing**:
- Automatically prevents "County County" duplication (e.g., "Orange County" input becomes "Orange County, Florida" not "Orange County County, Florida")
- Extracts clean tax ID numbers from complex strings like `"Broward County, FL Tax ID: 55-9988-777-6543"` → `"55-9988-777-6543"`

## Available Field Names

### Core Property Information
- `buyerName` - Buyer's name (automatically combined with sellerName into PARTIES field)
- `sellerName` - Seller's name (automatically combined with buyerName into PARTIES field)
- `parties` - Complete parties description (use this if you want to provide pre-formatted text)
- `county` - County name (automatically combined with propertyTaxID)
- `propertyTaxID` - Property tax ID number (automatically combined with county)
- `propertyDescription` - Property description
- `propertyLocation` - Property location
- `streetAddress` - Street address, city, zip
- `propertyAddress` - Property address
- `legalDescription1` - Legal description line 1
- `legalDescription2` - Legal description line 2
- `additionalAddress` - Additional address

**Automatic Field Combinations:**
- When you provide `buyerName` and `sellerName`, they are automatically formatted as: `"John Smith (\"Buyer\") and Sarah Johnson (\"Seller\")"`
- When you provide `county` and `propertyTaxID`, they are automatically formatted as: `"Miami-Dade County, Florida. Property Tax ID #: 12-3456-789-0123"`
- The API extracts clean tax ID numbers from strings like `"Miami-Dade County, FL Tax ID: 12-3456-789-0123"`
- Smart deduplication prevents "County County" in output (e.g., "Orange County" → "Orange County, Florida")

### Financial Fields
- `purchasePrice` - Purchase price
- `initialDeposit` - Initial deposit
- `additionalDeposit` - Additional deposit
- `balanceToClose` - Balance to close
- `earnestMoney` - Earnest money
- `inspectionFee` - Inspection fee
- `titleInsurance` - Title insurance
- `recordingFees` - Recording fees
- `closingCosts` - Closing costs
- `loanAmount` - Loan amount
- `downPayment` - Down payment
- `interestRate` - Interest rate
- `monthlyPayment` - Monthly payment
- `additionalFees` - Additional fees
- `prorationAmount` - Proration amount
- `escrowAmount` - Escrow amount
- `commissionAmount` - Commission amount
- `otherAmount` - Other amount
- `finalAmount` - Final amount

### Initials Fields
- `buyerInitials` - Buyer initials (page 1)
- `buyerInitials2` - Buyer initials (page 2)
- `sellerInitials` - Seller initials (page 1)
- `sellerInitials2` - Seller initials (page 2)
- `sellerInitials3` - Seller initials (page 3)
- `sellerInitials4` - Seller initials (page 4)

### Contact Information
- `escrowAgentName` - Escrow agent name
- `escrowAddress` - Escrow address
- `escrowEmail` - Escrow email
- `escrowFax` - Escrow fax
- `buyerAddress1` - Buyer address line 1
- `buyerAddress2` - Buyer address line 2
- `buyerAddress3` - Buyer address line 3
- `sellerAddress1` - Seller address line 1
- `sellerAddress2` - Seller address line 2
- `sellerAddress3` - Seller address line 3

### Dates
- `closingDate` - Closing date
- `contractDate` - Contract date
- `effectiveDate` - Effective date

### Real Estate Professionals
- `cooperatingBroker` - Cooperating broker
- `cooperatingSalesAssociate` - Cooperating sales associate
- `listingSalesAssociate` - Listing sales associate
- `listingBroker` - Listing broker

### Personal Property
- `includedPersonalProperty` - Included personal property
- `otherPersonalProperty` - Other personal property
- `excludedItems` - Excluded items
- `accessDevices` - Access devices

### Checkbox Fields (Financing Options)
Use `"true"` or `"false"` strings for checkbox values.

- `cashPurchase` - Cash purchase
- `financingContingent` - Financing contingent
- `conventionalFinancing` - Conventional financing
- `fhaFinancing` - FHA financing
- `vaFinancing` - VA financing
- `otherFinancing` - Other financing
- `fixedRate` - Fixed rate
- `adjustableRate` - Adjustable rate
- `assumptionMortgage` - Assumption mortgage
- `sellerFinancing` - Seller financing

### Title and Closing Checkboxes
- `sellerDesignatesClosing` - Seller designates closing
- `buyerDesignatesClosing` - Buyer designates closing
- `miamiDadeBroward` - Miami-Dade/Broward provision
- `buyerPaysWarranty` - Buyer pays warranty
- `sellerPaysWarranty` - Seller pays warranty
- `homeWarrantyNA` - Home warranty N/A

### Assessment Checkboxes
- `assessmentProration` - Assessment proration
- `assessmentPaidFull` - Assessment paid in full

### Special Provisions and Riders
- `condominiumRider` - Condominium rider
- `homeownersAssociation` - Homeowners association
- `sellerFinancingRider` - Seller financing rider
- `mortgageAssumption` - Mortgage assumption
- `fhaVaFinancing` - FHA/VA financing
- `appraisalContingency` - Appraisal contingency
- `shortSale` - Short sale
- `homeownersFloodIns` - Homeowners/flood insurance
- `defectiveDrywall` - Defective drywall
- `coastalConstruction` - Coastal construction
- `insulationDisclosure` - Insulation disclosure
- `leadPaintDisclosure` - Lead paint disclosure
- `housingOlderPersons` - Housing for older persons
- `rezoning` - Rezoning
- `leasePurchase` - Lease purchase
- `preClosingOccupancy` - Pre-closing occupancy
- `saleOfBuyersProperty` - Sale of buyer's property
- `backupContract` - Backup contract
- `sellersAttorneyApproval` - Seller's attorney approval
- `buyersAttorneyApproval` - Buyer's attorney approval
- `bindingArbitration` - Binding arbitration
- `miamiDadeCounty` - Miami-Dade County

### Generic Fields
- `checkbox1` through `checkbox4` - Generic checkboxes
- `specialField1` through `specialField14` - Special undefined fields

## Additional Endpoints

### GET `/health`
**Description**: Health check endpoint
**Response**: JSON status message

```bash
curl -X GET http://localhost:5002/health
```

### GET `/fields`
**Description**: Get list of all available field names
**Response**: JSON array of available field names

```bash
curl -X GET http://localhost:5002/fields
```

### GET `/test-sample`
**Description**: Generate a test PDF with sample data
**Response**: PDF file download with sample data

```bash
curl -X GET http://localhost:5002/test-sample --output sample.pdf
```

## Special Features

### Tax ID Extraction
The API automatically extracts tax ID numbers from combined strings:
- Input: `"Miami-Dade County, FL Tax ID: 12-3456-789-0123"`
- Extracted: `"12-3456-789-0123"`

### Checkbox Handling
Checkboxes accept various formats:
- `"true"`, `"1"`, `"yes"`, `"on"`, `"/yes"` → Checked
- `"false"`, `"0"`, `"no"`, `"off"` → Unchecked

### Error Handling
- Missing fields don't break the application
- Invalid field names are ignored
- Detailed error messages for debugging

## Response Codes

- `200` - Success (PDF generated)
- `400` - Bad Request (no form data provided)
- `500` - Internal Server Error (PDF generation failed)

## Legacy Compatibility

The API also supports legacy field names for backwards compatibility:
- `FIELD-PARTIES`
- `FIELD-PROPERTY-DESC`
- `FIELD-STREET-ADDRESS`
- `FIELD-COUNTY-TAX-ID`
- `FIELD-LEGAL-DESC-1`
- `FIELD-LEGAL-DESC-2`
