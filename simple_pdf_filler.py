#!/usr/bin/env python3
"""
Simple PDF Filler - Direct approach for fillable PDFs
No complex mapping, just direct field filling
"""

from flask import Flask, request, send_file, jsonify
from pdfrw import PdfReader, PdfWriter
import io
import json

app = Flask(__name__)

# Load the field structure
def load_pdf_fields():
    """Load the PDF field structure from JSON"""
    try:
        with open('pdf_fields.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("ERROR: pdf_fields.json not found")
        return {}

PDF_FIELDS = load_pdf_fields()
PDF_TEMPLATE_PATH = 'as is contract fillable.pdf'

def fill_pdf_simple(template_path, field_data):
    """Simple PDF filling - direct field access"""
    try:
        template_pdf = PdfReader(template_path)
        
        filled_count = 0
        
        # Iterate through all pages and annotations
        for page_num, page in enumerate(template_pdf.pages):
            if page.Annots:
                for annotation in page.Annots:
                    if annotation.T:  # Field has a name
                        # Get the field name (remove outer quotes if present)
                        field_name = str(annotation.T).strip("'\"")
                        clean_field_name = field_name.strip("()")
                        
                        # Check if we have data for this field
                        field_value = None
                        
                        # Try different field name variations
                        for key, value in field_data.items():
                            if (key == field_name or 
                                key == clean_field_name or 
                                key == f"({clean_field_name})" or
                                key == f"'({clean_field_name})'" or
                                clean_field_name == key):
                                field_value = value
                                break
                        
                        if field_value is not None:
                            # Handle different field types
                            if annotation.FT == '/Tx':  # Text field
                                annotation.V = str(field_value)
                                annotation.AP = ''  # Clear appearance
                                print(f"✅ Text: {clean_field_name} = '{field_value}'")
                                filled_count += 1
                                
                            elif annotation.FT == '/Btn':  # Button/Checkbox
                                if str(field_value).lower() in ['true', '1', 'yes', 'on']:
                                    # For checked state - try different values
                                    annotation.V = f'/{clean_field_name}'
                                    annotation.AS = f'/{clean_field_name}'
                                    print(f"✅ Checkbox: {clean_field_name} = CHECKED")
                                else:
                                    annotation.V = '/Off'
                                    annotation.AS = '/Off'
                                    print(f"☐ Checkbox: {clean_field_name} = UNCHECKED")
                                filled_count += 1
        
        print(f"🎉 Successfully filled {filled_count} fields")
        return template_pdf
        
    except Exception as e:
        print(f"❌ Error filling PDF: {str(e)}")
        raise e

@app.route('/simple-fill', methods=['POST'])
def simple_fill_endpoint():
    """Simple PDF filling endpoint"""
    try:
        # Get JSON data
        form_data = request.get_json()
        if not form_data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        print(f"📝 Received data for {len(form_data)} fields")
        
        # Fill the PDF
        filled_pdf = fill_pdf_simple(PDF_TEMPLATE_PATH, form_data)
        
        # Save to memory
        output_stream = io.BytesIO()
        PdfWriter(output_stream, trailer=filled_pdf).write()
        output_stream.seek(0)
        
        return send_file(
            output_stream,
            as_attachment=True,
            download_name='filled_contract.pdf',
            mimetype='application/pdf'
        )
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/fields', methods=['GET'])
def get_available_fields():
    """Get all available PDF fields"""
    try:
        # Extract clean field names
        clean_fields = {}
        for field_name, field_info in PDF_FIELDS.items():
            clean_name = field_name.strip("'\"()")
            clean_fields[clean_name] = {
                "original_name": field_name,
                "type": field_info.get("type", "unknown"),
                "page": field_info.get("page", 1)
            }
        
        return jsonify({
            "total_fields": len(clean_fields),
            "fields": clean_fields
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/test-simple', methods=['GET'])
def test_simple():
    """Test endpoint with sample data"""
    test_data = {
        "Text_1": "Jane Smith",
        "Text_2": "John Doe", 
        "Text_5": "123 Ocean Drive, Miami, FL",
        "Text_6": "Miami-Dade",
        "Text_7": "12-3456-789-0123",
        "Number_1": "450000",
        "Number_2": "45000",
        "Date_1": "2025-07-15",
        "Checkbox_1": "true",
        "Checkbox_2": "false",
        "Checkbox_3": "true",
        "Checkbox_9": "true",
        "Checkbox_16": "true",
        "Checkbox_17": "true",
        "Checkbox_18": "false"
    }
    
    try:
        filled_pdf = fill_pdf_simple(PDF_TEMPLATE_PATH, test_data)
        
        output_stream = io.BytesIO()
        PdfWriter(output_stream, trailer=filled_pdf).write()
        output_stream.seek(0)
        
        return send_file(
            output_stream,
            as_attachment=True,
            download_name='simple_test.pdf',
            mimetype='application/pdf'
        )
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/', methods=['GET'])
def home():
    """Home page with API info"""
    return jsonify({
        "message": "Simple PDF Filler API",
        "endpoints": {
            "/simple-fill": "POST - Fill PDF with JSON data",
            "/fields": "GET - List all available fields", 
            "/test-simple": "GET - Test with sample data"
        },
        "example": {
            "Text_1": "Seller Name",
            "Text_2": "Buyer Name",
            "Checkbox_1": "true",
            "Number_1": "450000"
        }
    })

if __name__ == '__main__':
    print("🚀 Starting Simple PDF Filler...")
    print(f"📄 Template: {PDF_TEMPLATE_PATH}")
    print(f"📊 Available fields: {len(PDF_FIELDS)}")
    app.run(host='127.0.0.1', port=5004, debug=True)
