function fillMockData() {
    const mockData = {
        'PARTIES': '<PERSON> (Buyer) and <PERSON> (Seller)',
        'buyer_name': '<PERSON>',
        'seller_name': '<PERSON>',
        'a Street address city zip': '123 Main Street, Miami, FL 33101',
        'County Florida Property Tax ID': 'Miami-Dade County, FL Tax ID: 12-3456-789-0123',
        'property_tax_id_only': '12-3456-789-0123',
        'c Real Property The legal description is 1': 'Lot 15, Block 3, SUNSET SUBDIVISION',
        'c Real Property The legal description is 2': 'according to the plat thereof recorded in Plat Book 45, Page 67',
        'and other access devices and storm shutterspanels Personal Property': 'Garage door openers, storm shutters, security system',
        'Other Personal Property items included in this purchase are': 'Refrigerator, washer, dryer, window treatments',
        'e The following items are excluded from the purchase': 'Personal artwork, family photos, outdoor furniture',
        'Escrow Agent Information Name': 'ABC Title Company',
        'Address': '456 Business Blvd, Miami, FL 33102',
        'Email': '<EMAIL>',
        'Fax': '(*************',
        'c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8': '$400,000',
        'Closing Date at the time established by the Closing Agent': 'December 15, 2024',
        'Buyers address for purposes of notice 1': '789 Buyer Lane',
        'Buyers address for purposes of notice 2': 'Miami, FL 33103',
        'Buyers address for purposes of notice 3': 'Phone: (*************',
        'Sellers address for purposes of notice 1': '321 Seller Street',
        'Sellers address for purposes of notice 2': 'Miami, FL 33104',
        'Sellers address for purposes of notice 3': 'Phone: (*************',
        'Listing Sales Associate': 'Mike Johnson',
        'Listing Broker': 'Premier Realty Group',
        'Cooperating Sales Associate if any': 'Sarah Williams',
        'Cooperating Broker if any': 'Elite Properties Inc.',
        'Buyers Initials': 'JS',
        'Sellers Initials': 'JD',
        'Date': '11/15/2024',
        'Date_2': '11/16/2024',
        'Date_3': '11/17/2024',
        'Date_4': '11/18/2024',
        '20 ADDITIONAL TERMS 1': 'Property to be sold in as-is condition',
        '20 ADDITIONAL TERMS 2': 'Buyer to conduct home inspection within 10 days',
        '20 ADDITIONAL TERMS 3': 'Seller to provide termite inspection report',
        'Text79': 'Sample text field 79',
        'Text80': 'Sample text field 80',
        'Text81': 'Sample text field 81',
        'Text103': 'Sample text field 103'
    };

    // Fill all the form fields with mock data
    Object.keys(mockData).forEach(fieldName => {
        const field = document.getElementById(fieldName) || document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.value = mockData[fieldName];
        }
    });

    // Update the field counter
    const event = new Event('input', { bubbles: true });
    document.querySelectorAll('input[type="text"], input[type="email"], textarea').forEach(input => {
        input.dispatchEvent(event);
    });

    alert('Mock data filled successfully! You can now generate the PDF.');
}

async function debugFields() {
    const form = document.getElementById('pdfForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to JSON object (include all fields, even empty ones for debugging)
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    try {
        const response = await fetch('/debug_fields', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error('Failed to debug fields');
        }

        const debugInfo = await response.json();

        // Create a detailed debug report
        let report = `=== PDF FIELD MAPPING DEBUG REPORT ===\n\n`;
        report += `Total form fields: ${debugInfo.total_form_fields}\n`;
        report += `Total PDF fields: ${debugInfo.total_pdf_fields}\n`;
        report += `Matching fields: ${Object.keys(debugInfo.matching_fields).length}\n`;
        report += `Non-matching fields: ${Object.keys(debugInfo.non_matching_fields).length}\n\n`;

        if (Object.keys(debugInfo.matching_fields).length > 0) {
            report += `MATCHING FIELDS:\n`;
            Object.keys(debugInfo.matching_fields).forEach(field => {
                const info = debugInfo.matching_fields[field];
                report += `- ${field}: "${info.form_value}" (PDF type: ${info.pdf_field_info.type})\n`;
            });
            report += `\n`;
        }

        if (Object.keys(debugInfo.non_matching_fields).length > 0) {
            report += `NON-MATCHING FIELDS (these won't be filled in PDF):\n`;
            Object.keys(debugInfo.non_matching_fields).forEach(field => {
                report += `- ${field}: "${debugInfo.non_matching_fields[field]}"\n`;
            });
            report += `\n`;
        }

        report += `ALL PDF FIELD NAMES:\n`;
        debugInfo.pdf_field_names.forEach(field => {
            report += `- ${field}\n`;
        });

        // Show in a modal or console
        console.log(report);
        alert('Debug information logged to console. Open browser developer tools to see detailed field mapping report.');

    } catch (error) {
        console.error('Debug error:', error);
        alert('An error occurred while debugging fields. Check console for details.');
    }
}

async function fillPDF() {
    const form = document.getElementById('pdfForm');
    const formData = new FormData(form);
    const data = {};
    
    // Convert FormData to JSON object
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            data[key] = value;
        }
    }
    
    if (Object.keys(data).length === 0) {
        alert('Please fill out at least one field before generating the PDF.');
        return;
    }
    
    try {
        // Show loading state
        const button = document.querySelector('.btn-primary');
        const originalText = button.textContent;
        button.textContent = 'Generating PDF...';
        button.disabled = true;
        
        const response = await fetch('/fill_pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error('Failed to generate PDF');
        }
        
        // Get the blob from the response
        const blob = await response.blob();
        
        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'filled_florida_agreement.pdf';
        
        // Trigger download
        document.body.appendChild(a);
        a.click();
        
        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // Reset button state
        button.textContent = originalText;
        button.disabled = false;
        
        // Show success message
        alert('PDF generated successfully! Check your downloads folder.');
        
    } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while generating the PDF. Please try again.');
        
        // Reset button state
        const button = document.querySelector('.btn-primary');
        button.textContent = 'Generate Filled PDF';
        button.disabled = false;
    }
}

async function fillPDFV2() {
    const form = document.getElementById('pdfForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to JSON object
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            data[key] = value;
        }
    }

    if (Object.keys(data).length === 0) {
        alert('Please fill out at least one field before generating the PDF.');
        return;
    }

    try {
        // Show loading state
        const button = document.querySelector('.btn-alternative');
        const originalText = button.textContent;
        button.textContent = 'Generating PDF (V2)...';
        button.disabled = true;

        const response = await fetch('/fill_pdf_v2', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to generate PDF: ${errorData.error}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'filled_florida_agreement_v2.pdf';

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Reset button state
        button.textContent = originalText;
        button.disabled = false;

        // Show success message
        alert('PDF generated successfully using alternative method! Check your downloads folder.');

    } catch (error) {
        console.error('Error:', error);
        alert(`An error occurred while generating the PDF: ${error.message}`);

        // Reset button state
        const button = document.querySelector('.btn-alternative');
        button.textContent = 'Try Alternative Method';
        button.disabled = false;
    }
}

async function fillPDFV4() {
    const form = document.getElementById('pdfForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to JSON object
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            data[key] = value;
        }
    }

    if (Object.keys(data).length === 0) {
        alert('Please fill out at least one field before generating the PDF.');
        return;
    }

    try {
        // Show loading state
        const button = document.querySelector('.btn-v4');
        const originalText = button.textContent;
        button.textContent = 'Generating PDF (V4)...';
        button.disabled = true;

        const response = await fetch('/fill_pdf_v4', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to generate PDF: ${errorData.error}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'filled_florida_agreement_v4.pdf';

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Reset button state
        button.textContent = originalText;
        button.disabled = false;

        // Show success message
        alert('PDF generated successfully using V4 method! Check your downloads folder.');

    } catch (error) {
        console.error('Error:', error);
        alert(`An error occurred while generating the PDF: ${error.message}`);

        // Reset button state
        const button = document.querySelector('.btn-v4');
        button.textContent = 'Try Method V4';
        button.disabled = false;
    }
}

async function testSimpleFill() {
    const form = document.getElementById('pdfForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to JSON object
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            data[key] = value;
        }
    }

    if (Object.keys(data).length === 0) {
        alert('Please fill out at least one field before testing.');
        return;
    }

    try {
        // Show loading state
        const button = document.querySelector('.btn-test');
        const originalText = button.textContent;
        button.textContent = 'Testing...';
        button.disabled = true;

        const response = await fetch('/test_simple_fill', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to test: ${errorData.error}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'test_simple_fill.pdf';

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Reset button state
        button.textContent = originalText;
        button.disabled = false;

        // Show success message
        alert('Test PDF generated! This only fills one field to test if basic filling works.');

    } catch (error) {
        console.error('Error:', error);
        alert(`Test failed: ${error.message}`);

        // Reset button state
        const button = document.querySelector('.btn-test');
        button.textContent = 'Test Simple Fill';
        button.disabled = false;
    }
}

async function fillPDFV5() {
    const form = document.getElementById('pdfForm');
    const formData = new FormData(form);
    const data = {};

    // Convert FormData to JSON object
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            data[key] = value;
        }
    }

    if (Object.keys(data).length === 0) {
        alert('Please fill out at least one field before generating the PDF.');
        return;
    }

    try {
        // Show loading state
        const button = document.querySelector('.btn-v5');
        const originalText = button.textContent;
        button.textContent = 'Generating PDF (V5)...';
        button.disabled = true;

        const response = await fetch('/fill_pdf_v5', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to generate PDF: ${errorData.error}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'filled_florida_agreement_v5.pdf';

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Reset button state
        button.textContent = originalText;
        button.disabled = false;

        // Show success message
        alert('PDF generated successfully using V5 enhanced method! Check your downloads folder.');

    } catch (error) {
        console.error('Error:', error);
        alert(`An error occurred while generating the PDF: ${error.message}`);

        // Reset button state
        const button = document.querySelector('.btn-v5');
        button.textContent = 'Method V5 (Enhanced)';
        button.disabled = false;
    }
}

async function debugPDFStructure() {
    try {
        const response = await fetch('/debug_pdf_structure', {
            method: 'GET'
        });

        if (!response.ok) {
            throw new Error('Failed to get PDF structure');
        }

        const structureInfo = await response.json();

        // Create detailed structure report
        let report = `=== PDF STRUCTURE DEBUG REPORT ===\n\n`;
        report += `Total pages: ${structureInfo.total_pages}\n\n`;

        structureInfo.pages.forEach(page => {
            report += `PAGE ${page.page_number}:\n`;
            report += `  Has annotations: ${page.has_annotations}\n`;
            report += `  Number of fields: ${page.fields.length}\n`;

            if (page.fields.length > 0) {
                report += `  Fields on this page:\n`;
                page.fields.forEach(field => {
                    report += `    - "${field.name}" (${field.type})\n`;
                    if (field.current_value) {
                        report += `      Current value: "${field.current_value}"\n`;
                    }
                    report += `      Position: ${field.rect}\n`;
                });
            }
            report += `\n`;
        });

        // Show in console and alert
        console.log(report);
        alert('PDF structure information logged to console. Open browser developer tools to see detailed page and field layout.');

    } catch (error) {
        console.error('Structure debug error:', error);
        alert('An error occurred while debugging PDF structure. Check console for details.');
    }
}

// Add enter key support for form submission
document.getElementById('pdfForm').addEventListener('keypress', function(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        fillPDF();
    }
});

// Auto-update combined fields
function updateCombinedFields() {
    const buyerName = document.getElementById('buyer_name').value.trim();
    const sellerName = document.getElementById('seller_name').value.trim();

    // Update PARTIES field
    if (buyerName || sellerName) {
        let parties = '';
        if (buyerName) parties += buyerName + ' (Buyer)';
        if (buyerName && sellerName) parties += ' and ';
        if (sellerName) parties += sellerName + ' (Seller)';
        document.getElementById('PARTIES').value = parties;
    }

    // Auto-extract tax ID from full county field
    const fullCountyField = document.getElementById('County Florida Property Tax ID').value.trim();
    const taxIdOnlyField = document.getElementById('property_tax_id_only');

    if (fullCountyField && !taxIdOnlyField.value.trim()) {
        // Extract tax ID using regex
        const taxIdMatch = fullCountyField.match(/Tax ID[:\s]*([0-9\-]+)/i);
        if (taxIdMatch) {
            taxIdOnlyField.value = taxIdMatch[1];
        }
    }
}

// Add field counter
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for auto-updating combined fields
    document.getElementById('buyer_name').addEventListener('input', updateCombinedFields);
    document.getElementById('seller_name').addEventListener('input', updateCombinedFields);
    document.getElementById('County Florida Property Tax ID').addEventListener('input', updateCombinedFields);
    const inputs = document.querySelectorAll('input[type="text"], input[type="email"], textarea');
    const counter = document.createElement('div');
    counter.style.position = 'fixed';
    counter.style.bottom = '20px';
    counter.style.right = '20px';
    counter.style.background = '#3498db';
    counter.style.color = 'white';
    counter.style.padding = '10px 20px';
    counter.style.borderRadius = '25px';
    counter.style.fontSize = '14px';
    counter.style.fontWeight = 'bold';
    counter.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    
    function updateCounter() {
        const filledFields = Array.from(inputs).filter(input => input.value.trim() !== '').length;
        counter.textContent = `${filledFields} / ${inputs.length} fields filled`;
    }
    
    inputs.forEach(input => {
        input.addEventListener('input', updateCounter);
    });
    
    updateCounter();
    document.body.appendChild(counter);
});