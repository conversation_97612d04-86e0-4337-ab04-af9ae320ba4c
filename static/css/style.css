* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
}

.subtitle {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.form-sections {
    display: grid;
    gap: 30px;
}

.form-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
}

.btn-mock {
    background-color: #e67e22;
    color: white;
}

.btn-mock:hover {
    background-color: #d35400;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(230, 126, 34, 0.3);
}

.btn-debug {
    background-color: #9b59b6;
    color: white;
}

.btn-debug:hover {
    background-color: #8e44ad;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
}

.btn-alternative {
    background-color: #16a085;
    color: white;
}

.btn-alternative:hover {
    background-color: #138d75;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(22, 160, 133, 0.3);
}

.btn-v4 {
    background-color: #e74c3c;
    color: white;
}

.btn-v4:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-test {
    background-color: #f39c12;
    color: white;
}

.btn-test:hover {
    background-color: #e67e22;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

.btn-v5 {
    background-color: #27ae60;
    color: white;
}

.btn-v5:hover {
    background-color: #229954;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-structure {
    background-color: #8e44ad;
    color: white;
}

.btn-structure:hover {
    background-color: #7d3c98;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(142, 68, 173, 0.3);
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .form-section {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}