<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Florida As-Is Purchase Agreement - PDF Field Mapper</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>Florida As-Is Purchase Agreement - PDF Field Mapper</h1>
        <p class="subtitle">Fill out any fields below (all are optional) and download your filled PDF</p>
        
        <form id="pdfForm">
            <div class="form-sections">
                <section class="form-section">
                    <h2>Parties Information</h2>
                    <div class="form-group">
                        <label for="PARTIES">Parties (Combined - Auto-filled)</label>
                        <input type="text" id="PARTIES" name="PARTIES" placeholder="Auto-generated from buyer and seller names" readonly>
                    </div>
                    <div class="form-group">
                        <label for="buyer_name">Buyer Name</label>
                        <input type="text" id="buyer_name" name="buyer_name" placeholder="Enter buyer name">
                    </div>
                    <div class="form-group">
                        <label for="seller_name">Seller Name</label>
                        <input type="text" id="seller_name" name="seller_name" placeholder="Enter seller name">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Property Details</h2>
                    <div class="form-group">
                        <label for="a Street address city zip">Property Address</label>
                        <input type="text" id="a Street address city zip" name="a Street address city zip" placeholder="Street address, city, zip">
                    </div>
                    <div class="form-group">
                        <label for="County Florida Property Tax ID">County & Property Tax ID (Full)</label>
                        <input type="text" id="County Florida Property Tax ID" name="County Florida Property Tax ID" placeholder="Miami-Dade County, FL Tax ID: 12-3456-789-0123">
                    </div>
                    <div class="form-group">
                        <label for="property_tax_id_only">Property Tax ID Only</label>
                        <input type="text" id="property_tax_id_only" name="property_tax_id_only" placeholder="12-3456-789-0123">
                    </div>
                    <div class="form-group">
                        <label for="c Real Property The legal description is 1">Legal Description Line 1</label>
                        <input type="text" id="c Real Property The legal description is 1" name="c Real Property The legal description is 1" placeholder="Legal description line 1">
                    </div>
                    <div class="form-group">
                        <label for="c Real Property The legal description is 2">Legal Description Line 2</label>
                        <input type="text" id="c Real Property The legal description is 2" name="c Real Property The legal description is 2" placeholder="Legal description line 2">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Personal Property</h2>
                    <div class="form-group">
                        <label for="and other access devices and storm shutterspanels Personal Property">Access Devices & Storm Shutters</label>
                        <input type="text" id="and other access devices and storm shutterspanels Personal Property" name="and other access devices and storm shutterspanels Personal Property" placeholder="Access devices and storm shutters/panels">
                    </div>
                    <div class="form-group">
                        <label for="Other Personal Property items included in this purchase are">Other Personal Property Included</label>
                        <textarea id="Other Personal Property items included in this purchase are" name="Other Personal Property items included in this purchase are" placeholder="List other personal property items included"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="e The following items are excluded from the purchase">Items Excluded from Purchase</label>
                        <textarea id="e The following items are excluded from the purchase" name="e The following items are excluded from the purchase" placeholder="List items excluded from the purchase"></textarea>
                    </div>
                </section>

                <section class="form-section">
                    <h2>Escrow Information</h2>
                    <div class="form-group">
                        <label for="Escrow Agent Information Name">Escrow Agent Name</label>
                        <input type="text" id="Escrow Agent Information Name" name="Escrow Agent Information Name" placeholder="Escrow agent name">
                    </div>
                    <div class="form-group">
                        <label for="Address">Escrow Agent Address</label>
                        <input type="text" id="Address" name="Address" placeholder="Escrow agent address">
                    </div>
                    <div class="form-group">
                        <label for="Email">Escrow Agent Email</label>
                        <input type="email" id="Email" name="Email" placeholder="Escrow agent email">
                    </div>
                    <div class="form-group">
                        <label for="Fax">Escrow Agent Fax</label>
                        <input type="text" id="Fax" name="Fax" placeholder="Escrow agent fax">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Financial Details</h2>
                    <div class="form-group">
                        <label for="c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8">Loan Amount</label>
                        <input type="text" id="c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8" name="c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8" placeholder="Enter loan amount or percentage">
                    </div>
                    <div class="form-group">
                        <label for="Closing Date at the time established by the Closing Agent">Closing Date</label>
                        <input type="text" id="Closing Date at the time established by the Closing Agent" name="Closing Date at the time established by the Closing Agent" placeholder="Closing date">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Additional Terms</h2>
                    {% for i in range(1, 17) %}
                    <div class="form-group">
                        <label for="20 ADDITIONAL TERMS {{ i }}">Additional Term {{ i }}</label>
                        <input type="text" id="20 ADDITIONAL TERMS {{ i }}" name="20 ADDITIONAL TERMS {{ i }}" placeholder="Additional term {{ i }}">
                    </div>
                    {% endfor %}
                </section>

                <section class="form-section">
                    <h2>Contact Information</h2>
                    <div class="form-group">
                        <label for="Buyers address for purposes of notice 1">Buyer's Address Line 1</label>
                        <input type="text" id="Buyers address for purposes of notice 1" name="Buyers address for purposes of notice 1" placeholder="Buyer's address line 1">
                    </div>
                    <div class="form-group">
                        <label for="Buyers address for purposes of notice 2">Buyer's Address Line 2</label>
                        <input type="text" id="Buyers address for purposes of notice 2" name="Buyers address for purposes of notice 2" placeholder="Buyer's address line 2">
                    </div>
                    <div class="form-group">
                        <label for="Buyers address for purposes of notice 3">Buyer's Address Line 3</label>
                        <input type="text" id="Buyers address for purposes of notice 3" name="Buyers address for purposes of notice 3" placeholder="Buyer's address line 3">
                    </div>
                    <div class="form-group">
                        <label for="Sellers address for purposes of notice 1">Seller's Address Line 1</label>
                        <input type="text" id="Sellers address for purposes of notice 1" name="Sellers address for purposes of notice 1" placeholder="Seller's address line 1">
                    </div>
                    <div class="form-group">
                        <label for="Sellers address for purposes of notice 2">Seller's Address Line 2</label>
                        <input type="text" id="Sellers address for purposes of notice 2" name="Sellers address for purposes of notice 2" placeholder="Seller's address line 2">
                    </div>
                    <div class="form-group">
                        <label for="Sellers address for purposes of notice 3">Seller's Address Line 3</label>
                        <input type="text" id="Sellers address for purposes of notice 3" name="Sellers address for purposes of notice 3" placeholder="Seller's address line 3">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Agent/Broker Information</h2>
                    <div class="form-group">
                        <label for="Listing Sales Associate">Listing Sales Associate</label>
                        <input type="text" id="Listing Sales Associate" name="Listing Sales Associate" placeholder="Listing sales associate name">
                    </div>
                    <div class="form-group">
                        <label for="Listing Broker">Listing Broker</label>
                        <input type="text" id="Listing Broker" name="Listing Broker" placeholder="Listing broker name">
                    </div>
                    <div class="form-group">
                        <label for="Cooperating Sales Associate if any">Cooperating Sales Associate</label>
                        <input type="text" id="Cooperating Sales Associate if any" name="Cooperating Sales Associate if any" placeholder="Cooperating sales associate (if any)">
                    </div>
                    <div class="form-group">
                        <label for="Cooperating Broker if any">Cooperating Broker</label>
                        <input type="text" id="Cooperating Broker if any" name="Cooperating Broker if any" placeholder="Cooperating broker (if any)">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Initials & Signatures</h2>
                    <div class="form-group">
                        <label for="Buyers Initials">Buyer's Initials</label>
                        <input type="text" id="Buyers Initials" name="Buyers Initials" placeholder="Buyer's initials">
                    </div>
                    <div class="form-group">
                        <label for="Sellers Initials">Seller's Initials</label>
                        <input type="text" id="Sellers Initials" name="Sellers Initials" placeholder="Seller's initials">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Dates</h2>
                    <div class="form-group">
                        <label for="Date">Date 1</label>
                        <input type="text" id="Date" name="Date" placeholder="Date">
                    </div>
                    <div class="form-group">
                        <label for="Date_2">Date 2</label>
                        <input type="text" id="Date_2" name="Date_2" placeholder="Date">
                    </div>
                    <div class="form-group">
                        <label for="Date_3">Date 3</label>
                        <input type="text" id="Date_3" name="Date_3" placeholder="Date">
                    </div>
                    <div class="form-group">
                        <label for="Date_4">Date 4</label>
                        <input type="text" id="Date_4" name="Date_4" placeholder="Date">
                    </div>
                </section>

                <section class="form-section">
                    <h2>Additional Text Fields</h2>
                    {% for i in range(79, 97) %}
                    <div class="form-group">
                        <label for="Text{{ i }}">Text Field {{ i }}</label>
                        <input type="text" id="Text{{ i }}" name="Text{{ i }}" placeholder="Text field {{ i }}">
                    </div>
                    {% endfor %}
                    <div class="form-group">
                        <label for="Text103">Text Field 103</label>
                        <input type="text" id="Text103" name="Text103" placeholder="Text field 103">
                    </div>
                </section>
            </div>

            <div class="form-actions">
                <button type="button" onclick="fillMockData()" class="btn btn-mock">Fill Mock Data</button>
                <button type="button" onclick="testSimpleFill()" class="btn btn-test">Test Simple Fill</button>
                <button type="button" onclick="fillPDFV6()" class="btn btn-v6">Method V6 (Smart Mapping)</button>
                <button type="button" onclick="fillPDFV4()" class="btn btn-v4">Method V4 (Working)</button>
                <button type="button" onclick="debugPDFStructure()" class="btn btn-structure">Debug PDF Structure</button>
                <button type="button" onclick="debugFields()" class="btn btn-debug">Debug Fields</button>
                <button type="reset" class="btn btn-secondary">Clear All Fields</button>
            </div>
        </form>
    </div>

    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>