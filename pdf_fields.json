{"'(THIS FORM HAS BEEN APPROVED BY THE FLORIDA REALTORS AND THE FLORIDA BAR)'": {"name": "'(THIS FORM HAS BEEN APPROVED BY THE FLORIDA REALTORS AND THE FLORIDA BAR)'", "type": "/Tx", "current_value": "", "page": 1}, "'(PARTIES)'": {"name": "'(PARTIES)'", "type": "/Tx", "current_value": "", "page": 1}, "'(PROPERTY DESCRIPTION)'": {"name": "'(PROPERTY DESCRIPTION)'", "type": "/Tx", "current_value": "", "page": 1}, "'(a Street address city zip)'": {"name": "'(a Street address city zip)'", "type": "/Tx", "current_value": "", "page": 1}, "'(County Florida Property Tax ID)'": {"name": "'(County Florida Property Tax ID)'", "type": "/Tx", "current_value": "", "page": 1}, "'(c Real Property The legal description is 1)'": {"name": "'(c Real Property The legal description is 1)'", "type": "/Tx", "current_value": "", "page": 1}, "'(c Real Property The legal description is 2)'": {"name": "'(c Real Property The legal description is 2)'", "type": "/Tx", "current_value": "", "page": 1}, "'(and other access devices and storm shutterspanels Personal Property)'": {"name": "'(and other access devices and storm shutterspanels Personal Property)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Other Personal Property items included in this purchase are)'": {"name": "'(Other Personal Property items included in this purchase are)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Personal Property is included in the Purchase Price has no contributory value and shall be left for the Buyer)'": {"name": "'(Personal Property is included in the Purchase Price has no contributory value and shall be left for the Buyer)'", "type": "/Tx", "current_value": "", "page": 1}, "'(e The following items are excluded from the purchase)'": {"name": "'(e The following items are excluded from the purchase)'", "type": "/Tx", "current_value": "", "page": 1}, "'(accompanies offer or ii)'": {"name": "'(accompanies offer or ii)'", "type": "/Btn", "current_value": "", "page": 1}, "'(blank then 3 days after Effective Date IF NEITHER BOX IS CHECKED THEN)'": {"name": "'(blank then 3 days after Effective Date IF NEITHER BOX IS CHECKED THEN)'", "type": "/Btn", "current_value": "", "page": 1}, "'(OPTION ii SHALL BE DEEMED SELECTED)'": {"name": "'(OPTION ii SHALL BE DEEMED SELECTED)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Escrow Agent Information Name)'": {"name": "'(Escrow Agent Information Name)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Address)'": {"name": "'(Address)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Email)'": {"name": "'(<PERSON><PERSON>)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Fax)'": {"name": "'(Fax)'", "type": "/Tx", "current_value": "", "page": 1}, "'(undefined_2)'": {"name": "'(undefined_2)'", "type": "/Tx", "current_value": "", "page": 1}, "'(c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8)'": {"name": "'(c Financing Express as a dollar amount or percentage Loan Amount see Paragraph 8)'", "type": "/Tx", "current_value": "", "page": 1}, "'(FloridaRealtorsFloridaBarASIS5x)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved)'": {"name": "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Closing Date at the time established by the Closing Agent)'": {"name": "'(Closing Date at the time established by the Closing Agent)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Sellers Initials)'": {"name": "'(<PERSON><PERSON> Initials)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text79)'": {"name": "'(Text79)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text80)'": {"name": "'(Text80)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text81)'": {"name": "'(Text81)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text82)'": {"name": "'(Text82)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text83)'": {"name": "'(Text83)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text84)'": {"name": "'(Text84)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text85)'": {"name": "'(Text85)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text86)'": {"name": "'(Text86)'", "type": "/Tx", "current_value": "", "page": 1}, "'(Text87)'": {"name": "'(Text87)'", "type": "/Tx", "current_value": "", "page": 1}, "'(CHECK IF PROPERTY IS SUBJECT TO LEASES OR OCCUPANCY AFTER CLOSING If Property is)'": {"name": "'(CHECK IF PROPERTY IS SUBJECT TO LEASES OR OCCUPANCY AFTER CLOSING If Property is)'", "type": "/Btn", "current_value": "", "page": 2}, "'(may assign but not be released from liability under this Contract or)'": {"name": "'(may assign but not be released from liability under this Contract or)'", "type": "/Btn", "current_value": "", "page": 2}, "'(may not assign this)'": {"name": "'(may not assign this)'", "type": "/Btn", "current_value": "", "page": 2}, "'(may assign and thereby be released from any further liability under)'": {"name": "'(may assign and thereby be released from any further liability under)'", "type": "/Btn", "current_value": "", "page": 2}, "'(a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers)'": {"name": "'(a Buyer will pay cash for the purchase of the Property at Closing There is no financing contingency to Buyers)'", "type": "/Btn", "current_value": "", "page": 2}, "'(b This Contract is contingent upon Buyer obtaining approval of a)'": {"name": "'(b This Contract is contingent upon Buyer obtaining approval of a)'", "type": "/Btn", "current_value": "", "page": 2}, "'(conventional)'": {"name": "'(conventional)'", "type": "/Btn", "current_value": "", "page": 2}, "'(FHA)'": {"name": "'(FHA)'", "type": "/Btn", "current_value": "", "page": 2}, "'(VA or)'": {"name": "'(VA or)'", "type": "/Btn", "current_value": "", "page": 2}, "'(other)'": {"name": "'(other)'", "type": "/Btn", "current_value": "", "page": 2}, "'(fixed)'": {"name": "'(fixed)'", "type": "/Btn", "current_value": "", "page": 2}, "'(adjustable)'": {"name": "'(adjustable)'", "type": "/Btn", "current_value": "", "page": 2}, "'(fixed or adjustable rate in the Loan Amount See Paragraph)'": {"name": "'(fixed or adjustable rate in the Loan Amount See Paragraph)'", "type": "/Btn", "current_value": "", "page": 2}, "'(if left blank then 5 days)'": {"name": "'(if left blank then 5 days)'", "type": "/Tx", "current_value": "", "page": 2}, "'(1 waive Loan Approval in which event this Contract will continue as if Loan Approval had been obtained or)'": {"name": "'(1 waive <PERSON><PERSON> in which event this Contract will continue as if <PERSON><PERSON> had been obtained or)'", "type": "/Tx", "current_value": "", "page": 2}, "'(2 terminate this Contract)'": {"name": "'(2 terminate this Contract)'", "type": "/Tx", "current_value": "", "page": 2}, "'(FloridaRealtorsFloridaBarASIS5x_2)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_2)'", "type": "/Tx", "current_value": "", "page": 2}, "'(undefined_3)'": {"name": "'(undefined_3)'", "type": "/Tx", "current_value": "", "page": 2}, "'(Text88)'": {"name": "'(Text88)'", "type": "/Tx", "current_value": "", "page": 2}, "'(Text89)'": {"name": "'(Text89)'", "type": "/Tx", "current_value": "", "page": 2}, "'(Text90)'": {"name": "'(Text90)'", "type": "/Tx", "current_value": "", "page": 2}, "'(Text91)'": {"name": "'(Text91)'", "type": "/Tx", "current_value": "", "page": 2}, "'(c Assumption of existing mortgage see rider for terms)'": {"name": "'(c Assumption of existing mortgage see rider for terms)'", "type": "/Btn", "current_value": "", "page": 3}, "'(d Purchase money note and mortgage to Seller see riders addenda or special clauses for terms)'": {"name": "'(d Purchase money note and mortgage to <PERSON><PERSON> see riders addenda or special clauses for terms)'", "type": "/Btn", "current_value": "", "page": 3}, "'(Other)'": {"name": "'(Other)'", "type": "/Tx", "current_value": "", "page": 3}, "'(Other_2)'": {"name": "'(Other_2)'", "type": "/Tx", "current_value": "", "page": 3}, "'(i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the)'": {"name": "'(i Seller shall designate Closing Agent and pay for Owners Policy and Charges and Buyer shall pay the)'", "type": "/Btn", "current_value": "", "page": 3}, "'(ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing)'": {"name": "'(ii Buyer shall designate Closing Agent and pay for Owners Policy and Charges and charges for closing)'", "type": "/Btn", "current_value": "", "page": 3}, "'(FloridaRealtorsFloridaBarASIS5x_3)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_3)'", "type": "/Tx", "current_value": "", "page": 3}, "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_2)'": {"name": "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_2)'", "type": "/Tx", "current_value": "", "page": 3}, "'(Sellers Initials_2)'": {"name": "'(Sellers Initials_2)'", "type": "/Tx", "current_value": "", "page": 3}, "'(undefined_4)'": {"name": "'(undefined_4)'", "type": "/Tx", "current_value": "", "page": 3}, "'(Text92)'": {"name": "'(Text92)'", "type": "/Tx", "current_value": "", "page": 3}, "'(iii MIAMIDADEBROWARD REGIONAL PROVISION Seller shall furnish a copy of a prior owners policy)'": {"name": "'(iii MIAMIDADEBROWARD REGIONAL PROVISION Seller shall furnish a copy of a prior owners policy)'", "type": "/Btn", "current_value": "", "page": 4}, "'(Buyer)'": {"name": "'(Buyer)'", "type": "/Btn", "current_value": "", "page": 4}, "'(Seller)'": {"name": "'(<PERSON><PERSON>)'", "type": "/Btn", "current_value": "", "page": 4}, "'(NA shall pay for a home warranty plan issued by)'": {"name": "'(NA shall pay for a home warranty plan issued by)'", "type": "/Btn", "current_value": "", "page": 4}, "'(warranty plan provides for repair or replacement of many of a homes mechanical systems and major builtin)'": {"name": "'(warranty plan provides for repair or replacement of many of a homes mechanical systems and major builtin)'", "type": "/Tx", "current_value": "", "page": 4}, "'(a Seller shall pay installments due prior to Closing and Buyer shall pay installments due after Closing)'": {"name": "'(a Seller shall pay installments due prior to Closing and Buyer shall pay installments due after Closing)'", "type": "/Btn", "current_value": "", "page": 4}, "'(b Seller shall pay the assessments in full prior to or at the time of Closing)'": {"name": "'(b Seller shall pay the assessments in full prior to or at the time of Closing)'", "type": "/Btn", "current_value": "", "page": 4}, "'(FloridaRealtorsFloridaBarASIS5x_4)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_4)'", "type": "/Tx", "current_value": "", "page": 4}, "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_3)'": {"name": "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_3)'", "type": "/Tx", "current_value": "", "page": 4}, "'(Sellers Initials_3)'": {"name": "'(Sellers Initials_3)'", "type": "/Tx", "current_value": "", "page": 4}, "'(undefined_5)'": {"name": "'(undefined_5)'", "type": "/Tx", "current_value": "", "page": 4}, "'(Text93)'": {"name": "'(Text93)'", "type": "/Tx", "current_value": "", "page": 4}, "'(Text94)'": {"name": "'(Text94)'", "type": "/Tx", "current_value": "", "page": 4}, "'(Text95)'": {"name": "'(Text95)'", "type": "/Tx", "current_value": "", "page": 4}, "'(such Permit issues Sellers obligation to cooperate shall include Sellers execution of necessary authorizations)'": {"name": "'(such Permit issues Sellers obligation to cooperate shall include Sellers execution of necessary authorizations)'", "type": "/Tx", "current_value": "", "page": 5}, "'(FloridaRealtorsFloridaBarASIS5x_5)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_5)'", "type": "/Tx", "current_value": "", "page": 5}, "'(Sellers Initials_4)'": {"name": "'(Sellers Initials_4)'", "type": "/Tx", "current_value": "", "page": 5}, "'(undefined_6)'": {"name": "'(undefined_6)'", "type": "/Tx", "current_value": "", "page": 5}, "'(Text96)'": {"name": "'(Text96)'", "type": "/Tx", "current_value": "", "page": 5}, "'(in full settlement of any claims whereupon Buyer and Seller shall be relieved from all further obligations under)'": {"name": "'(in full settlement of any claims whereupon Buyer and Seller shall be relieved from all further obligations under)'", "type": "/Tx", "current_value": "", "page": 6}, "'(FloridaRealtorsFloridaBarASIS5x_6)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_6)'", "type": "/Tx", "current_value": "", "page": 6}, "'(Sellers Initials_5)'": {"name": "'(Sellers Initials_5)'", "type": "/Tx", "current_value": "", "page": 6}, "'(undefined_7)'": {"name": "'(undefined_7)'", "type": "/Tx", "current_value": "", "page": 6}, "'(FloridaRealtorsFloridaBarASIS5x_7)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_7)'", "type": "/Tx", "current_value": "", "page": 7}, "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_4)'": {"name": "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_4)'", "type": "/Tx", "current_value": "", "page": 7}, "'(Sellers Initials_6)'": {"name": "'(Sellers Initials_6)'", "type": "/Tx", "current_value": "", "page": 7}, "'(undefined_8)'": {"name": "'(undefined_8)'", "type": "/Tx", "current_value": "", "page": 7}, "'(FloridaRealtorsFloridaBarASIS5x_8)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_8)'", "type": "/Tx", "current_value": "", "page": 8}, "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_5)'": {"name": "'(Rev619  2017 Florida Realtors and The Florida Bar All rights reserved_5)'", "type": "/Tx", "current_value": "", "page": 8}, "'(Sellers Initials_7)'": {"name": "'(Sellers Initials_7)'", "type": "/Tx", "current_value": "", "page": 8}, "'(undefined_9)'": {"name": "'(undefined_9)'", "type": "/Tx", "current_value": "", "page": 8}, "'(pursuant to terms of this Contract If restoration is not completed as of Closing a sum equal to 125 of estimated)'": {"name": "'(pursuant to terms of this Contract If restoration is not completed as of Closing a sum equal to 125 of estimated)'", "type": "/Tx", "current_value": "", "page": 9}, "'(FloridaRealtorsFloridaBarASIS5x_9)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_9)'", "type": "/Tx", "current_value": "", "page": 9}, "'(Sellers Initials_8)'": {"name": "'(Sellers Initials_8)'", "type": "/Tx", "current_value": "", "page": 9}, "'(undefined_10)'": {"name": "'(undefined_10)'", "type": "/Tx", "current_value": "", "page": 9}, "'(escrow at Sellers expense with an escrow agent selected by Buyer and pursuant to terms negotiated by the)'": {"name": "'(escrow at Sellers expense with an escrow agent selected by Buyer and pursuant to terms negotiated by the)'", "type": "/Tx", "current_value": "", "page": 10}, "'(FloridaRealtorsFloridaBarASIS5x_10)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_10)'", "type": "/Tx", "current_value": "", "page": 10}, "'(Sellers Initials_9)'": {"name": "'(Sellers Initials_9)'", "type": "/Tx", "current_value": "", "page": 10}, "'(undefined_11)'": {"name": "'(undefined_11)'", "type": "/Tx", "current_value": "", "page": 10}, "'(K RESERVED)'": {"name": "'(K RESERVED)'", "type": "/Btn", "current_value": "", "page": 11}, "'(L RESERVED)'": {"name": "'(L RESERVED)'", "type": "/Btn", "current_value": "", "page": 11}, "'(M Defective Drywall)'": {"name": "'(M Defective Drywall)'", "type": "/Btn", "current_value": "", "page": 11}, "'(N Coastal Construction Control)'": {"name": "'(N Coastal Construction Control)'", "type": "/Btn", "current_value": "", "page": 11}, "'(O Insulation Disclosure)'": {"name": "'(O Insulation Disclosure)'", "type": "/Btn", "current_value": "", "page": 11}, "'(P Lead Paint Disclosure Pre1978)'": {"name": "'(P Lead Paint Disclosure Pre1978)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Q Housing for Older Persons)'": {"name": "'(Q Housing for Older Persons)'", "type": "/Btn", "current_value": "", "page": 11}, "'(R Rezoning)'": {"name": "'(R Rezoning)'", "type": "/Btn", "current_value": "", "page": 11}, "'(S Lease Purchase Lease Option)'": {"name": "'(S Lease Purchase Lease Option)'", "type": "/Btn", "current_value": "", "page": 11}, "'(A Condominium Rider)'": {"name": "'(A Condominium Rider)'", "type": "/Btn", "current_value": "", "page": 11}, "'(B Homeowners Assn)'": {"name": "'(B Homeowners Assn)'", "type": "/Btn", "current_value": "", "page": 11}, "'(C Seller Financing)'": {"name": "'(<PERSON> Financing)'", "type": "/Btn", "current_value": "", "page": 11}, "'(D Mortgage Assumption)'": {"name": "'(D Mortgage Assumption)'", "type": "/Btn", "current_value": "", "page": 11}, "'(E FHAVA Financing)'": {"name": "'(E FHAVA Financing)'", "type": "/Btn", "current_value": "", "page": 11}, "'(F Appraisal Contingency)'": {"name": "'(F Appraisal Contingency)'", "type": "/Btn", "current_value": "", "page": 11}, "'(G Short Sale)'": {"name": "'(<PERSON>)'", "type": "/Btn", "current_value": "", "page": 11}, "'(H HomeownersFlood Ins)'": {"name": "'(H HomeownersFlood Ins)'", "type": "/Btn", "current_value": "", "page": 11}, "'(I)'": {"name": "'(I)'", "type": "/Btn", "current_value": "", "page": 11}, "'(J)'": {"name": "'(J)'", "type": "/Btn", "current_value": "", "page": 11}, "'(T PreClosing Occupancy)'": {"name": "'(T PreClosing Occupancy)'", "type": "/Btn", "current_value": "", "page": 11}, "'(V Sale of Buyers Property)'": {"name": "'(V Sale of Buyers Property)'", "type": "/Btn", "current_value": "", "page": 11}, "'(W Backup Contract)'": {"name": "'(W Backup Contract)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Y Sellers Attorney Approval)'": {"name": "'(<PERSON>)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Z Buyers Attorney Approval)'": {"name": "'(Z Buyers Attorney App<PERSON>al)'", "type": "/Btn", "current_value": "", "page": 11}, "'(BB Binding Arbitration)'": {"name": "'(BB Binding Arbitration)'", "type": "/Btn", "current_value": "", "page": 11}, "'(CC MiamiDade County)'": {"name": "'(CC MiamiDade County)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Other_3)'": {"name": "'(Other_3)'", "type": "/Tx", "current_value": "", "page": 11}, "'(undefined_12)'": {"name": "'(undefined_12)'", "type": "/Tx", "current_value": "", "page": 11}, "'(undefined_13)'": {"name": "'(undefined_13)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 1)'": {"name": "'(20 ADDITIONAL TERMS 1)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 2)'": {"name": "'(20 ADDITIONAL TERMS 2)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 3)'": {"name": "'(20 ADDITIONAL TERMS 3)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 4)'": {"name": "'(20 ADDITIONAL TERMS 4)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 5)'": {"name": "'(20 ADDITIONAL TERMS 5)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 6)'": {"name": "'(20 ADDITIONAL TERMS 6)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 7)'": {"name": "'(20 ADDITIONAL TERMS 7)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 8)'": {"name": "'(20 ADDITIONAL TERMS 8)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 9)'": {"name": "'(20 ADDITIONAL TERMS 9)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 10)'": {"name": "'(20 ADDITIONAL TERMS 10)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 11)'": {"name": "'(20 ADDITIONAL TERMS 11)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 12)'": {"name": "'(20 ADDITIONAL TERMS 12)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 13)'": {"name": "'(20 ADDITIONAL TERMS 13)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 14)'": {"name": "'(20 ADDITIONAL TERMS 14)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 15)'": {"name": "'(20 ADDITIONAL TERMS 15)'", "type": "/Tx", "current_value": "", "page": 11}, "'(20 ADDITIONAL TERMS 16)'": {"name": "'(20 ADDITIONAL TERMS 16)'", "type": "/Tx", "current_value": "", "page": 11}, "'(Seller counters Buyers offer to accept the counteroffer Buyer must sign or initial the counteroffered terms and)'": {"name": "'(Seller counters Buyers offer to accept the counteroffer Buyer must sign or initial the counteroffered terms and)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Seller rejects Buyers offer)'": {"name": "'(<PERSON><PERSON> rejects Buyers offer)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Buyers Initials)'": {"name": "'(Buyers Initials)'", "type": "/Tx", "current_value": "", "page": 11}, "'(FloridaRealtorsFloridaBarASIS5x_11)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_11)'", "type": "/Tx", "current_value": "", "page": 11}, "'(Sellers Initials_10)'": {"name": "'(Sellers Initials_10)'", "type": "/Tx", "current_value": "", "page": 11}, "'(undefined_14)'": {"name": "'(undefined_14)'", "type": "/Tx", "current_value": "", "page": 11}, "'(Check Box97)'": {"name": "'(Check Box97)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Check Box99)'": {"name": "'(Check Box99)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Check Box101)'": {"name": "'(Check Box101)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Check Box102)'": {"name": "'(Check Box102)'", "type": "/Btn", "current_value": "", "page": 11}, "'(Text103)'": {"name": "'(Text103)'", "type": "/Tx", "current_value": "", "page": 11}, "'(Date)'": {"name": "'(Date)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Date_2)'": {"name": "'(Date_2)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Date_3)'": {"name": "'(Date_3)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Date_4)'": {"name": "'(Date_4)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Buyers address for purposes of notice 1)'": {"name": "'(Buyers address for purposes of notice 1)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Buyers address for purposes of notice 2)'": {"name": "'(Buyers address for purposes of notice 2)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Buyers address for purposes of notice 3)'": {"name": "'(Buyers address for purposes of notice 3)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Sellers address for purposes of notice 1)'": {"name": "'(Sellers address for purposes of notice 1)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Sellers address for purposes of notice 2)'": {"name": "'(Sellers address for purposes of notice 2)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Sellers address for purposes of notice 3)'": {"name": "'(Sellers address for purposes of notice 3)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Cooperating Sales Associate if any)'": {"name": "'(Cooperating Sales Associate if any)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Listing Sales Associate)'": {"name": "'(Listing Sales Associate)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Cooperating Broker if any)'": {"name": "'(Cooper<PERSON> <PERSON><PERSON><PERSON> if any)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Buyers Initials_2)'": {"name": "'(Buyers Initials_2)'", "type": "/Tx", "current_value": "", "page": 12}, "'(FloridaRealtorsFloridaBarASIS5x_12)'": {"name": "'(FloridaRealtorsFloridaBarASIS5x_12)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Listing Broker)'": {"name": "'(Listing Broker)'", "type": "/Tx", "current_value": "", "page": 12}, "'(Sellers Initials_11)'": {"name": "'(Sellers Initials_11)'", "type": "/Tx", "current_value": "", "page": 12}, "'(undefined_15)'": {"name": "'(undefined_15)'", "type": "/Tx", "current_value": "", "page": 12}}