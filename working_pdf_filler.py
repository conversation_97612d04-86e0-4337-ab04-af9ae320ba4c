#!/usr/bin/env python3
"""
Working PDF Filler with proper checkbox support using fillpdf library
"""

from fillpdf import fillpdfs
from flask import Flask, request, send_file, jsonify
import io
import os
import tempfile

app = Flask(__name__)

PDF_TEMPLATE_PATH = 'as is contract fillable.pdf'

def fill_pdf_working(template_path, field_data):
    """Fill PDF using fillpdf library with proper checkbox support"""
    
    print(f"📝 Received data for {len(field_data)} fields")
    
    # Convert checkbox values to proper format
    processed_data = {}
    
    for field_name, field_value in field_data.items():
        if field_name.startswith('Checkbox_'):
            # Convert boolean-like values to "Yes" for checkboxes
            if str(field_value).lower() in ['true', '1', 'yes', 'on']:
                processed_data[field_name] = "Yes"
                print(f"✅ Checkbox: {field_name} = CHECKED")
            else:
                processed_data[field_name] = "Off"
                print(f"☐ Checkbox: {field_name} = UNCHECKED")
        else:
            # Handle other field types normally
            processed_data[field_name] = str(field_value)
            print(f"📝 Field: {field_name} = '{field_value}'")
    
    # Create temporary output file
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # Fill the PDF using fillpdf
        fillpdfs.write_fillable_pdf(template_path, temp_path, processed_data)
        
        # Read the filled PDF
        with open(temp_path, 'rb') as f:
            filled_pdf_bytes = f.read()
        
        print(f"🎉 Successfully filled PDF with {len(processed_data)} fields")
        return filled_pdf_bytes
        
    finally:
        # Clean up temporary file
        if os.path.exists(temp_path):
            os.unlink(temp_path)

@app.route('/')
def home():
    return jsonify({
        "message": "Working PDF Filler API with Checkbox Support",
        "endpoints": {
            "/fill": "POST - Fill PDF with JSON data (checkboxes working!)",
            "/fields": "GET - List all available fields",
            "/test": "GET - Test with sample data"
        },
        "example": {
            "Text_1": "Seller Name",
            "Text_2": "Buyer Name",
            "Text_3": "123 Main St, Miami, FL",
            "Number_1": "450000",
            "Checkbox_8": "true",
            "Checkbox_9": "true",
            "Checkbox_16": "true"
        }
    })

@app.route('/fill', methods=['POST'])
def fill_pdf():
    """Fill PDF with provided data"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Fill the PDF
        filled_pdf_bytes = fill_pdf_working(PDF_TEMPLATE_PATH, data)
        
        # Return the filled PDF
        return send_file(
            io.BytesIO(filled_pdf_bytes),
            mimetype='application/pdf',
            as_attachment=True,
            download_name='filled_contract.pdf'
        )
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/fields')
def list_fields():
    """List all available PDF fields"""
    try:
        fields = fillpdfs.get_form_fields(PDF_TEMPLATE_PATH)
        return jsonify({
            "total_fields": len(fields),
            "fields": fields
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/test')
def test_fill():
    """Test with sample data"""
    test_data = {
        "Text_1": "Sarah Johnson",
        "Text_2": "Michael Rodriguez", 
        "Text_3": "123 Ocean Drive, Miami Beach, FL 33139",
        "Text_4": "Miami-Dade",
        "Text_5": "12-3456-789-0123", 
        "Text_6": "Lot 15, Block 3, SUNSET ESTATES SUBDIVISION",
        "Number_1": "750000",
        "Number_2": "75000",
        "Date_1": "2025-07-15",
        "Checkbox_8": "true",    # Cash
        "Checkbox_9": "true",    # Conventional
        "Checkbox_16": "true",   # Refrigerator
        "Checkbox_17": "true",   # Washer
        "Checkbox_18": "true",   # Dryer
    }
    
    try:
        filled_pdf_bytes = fill_pdf_working(PDF_TEMPLATE_PATH, test_data)
        
        return send_file(
            io.BytesIO(filled_pdf_bytes),
            mimetype='application/pdf',
            as_attachment=True,
            download_name='test_filled_contract.pdf'
        )
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting Working PDF Filler with Checkbox Support...")
    print(f"📄 Template: {PDF_TEMPLATE_PATH}")
    
    # Check if template exists
    if not os.path.exists(PDF_TEMPLATE_PATH):
        print(f"❌ Template not found: {PDF_TEMPLATE_PATH}")
        exit(1)
    
    try:
        fields = fillpdfs.get_form_fields(PDF_TEMPLATE_PATH)
        print(f"📊 Available fields: {len(fields)}")
    except Exception as e:
        print(f"⚠️ Could not read fields: {e}")
    
    app.run(host='127.0.0.1', port=5006, debug=True)
